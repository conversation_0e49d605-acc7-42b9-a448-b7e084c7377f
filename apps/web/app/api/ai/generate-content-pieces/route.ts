import { NextRequest } from 'next/server';
import OpenAI from 'openai';
import { z } from 'zod';

const OPENAI_API_KEY =
  '********************************************************************************************************************************************************************';

const openai = new OpenAI({
  apiKey: OPENAI_API_KEY, // Store your API key in an environment variable
});

const ContentTypeEnum = z.enum([
  'Tweet',
  'Blog Post',
  'LinkedIn Post',
  'Content Share Email',
]);
const ChannelEnum = z.enum(['Twitter', 'Blogs', 'LinkedIn', 'Email']);
const LanguageEnum = z.enum(['en', 'es', 'fr']); // add more languages as needed

const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  content_type: ContentTypeEnum,
  channel: ChannelEnum,
  is_scheduled: z.boolean(),
  is_posted: z.boolean(),
  created_at: z.string(),
  scheduled_for: z.string().nullable(),
  language: LanguageEnum,
  hasVisualContent: z.boolean(),
  status: z.string(),
  content: z.object({
    subject: z.string().optional(),
    body: z.string(),
    exercise: z.string().optional(),
    cta: z.string().optional(),
    visualDescription: z.string().optional(),
  }),
});

const TaskListSchema = z.array(TaskSchema);

type Task = z.infer<typeof TaskSchema>;
type TaskList = z.infer<typeof TaskListSchema>;

interface RequestBody {
  creativeBrief: string;
  language: string;
}

/**
 * @name POST
 * @description POST handler
 */
export const POST = async (request: NextRequest) => {
  try {
    const body = (await request.json()) as RequestBody;

    const currentTimestamp = new Date().toISOString();
    const prompt = `
      You are an API that takes a marketing creative brief as input and generates tasks for a kanban board or calendar. 
      Each task should be an actionable post, and derived from the content strategy or execution steps in the creative brief.  
      Ensure you capture as many tasks as the creative brief indicates.
      Each task should include:
        - An ID (generate a unique string)
        - A title
        - A content_type (must be one of: Tweet, Blog Post, LinkedIn Post, Video)
        - A channel (must be one of: Twitter, Blogs, LinkedIn, Email)
        - hasVisualContent (boolean indicating if this content piece needs visual assets)
        - A status (e.g., To Do, In Progress, Done)
        - Content draft including:
          * subject (optional)
          * description (required) - Description of the content only. Do not create the content itself (this will be generated at a later separately)
          * visualDescription (required if hasVisualContent is true)

      Additional fields will be automatically set:
        - is_scheduled: false
        - is_posted: false
        - created_at: current timestamp
        - scheduled_for: null
        - language: "${body.language}"

        do not send it back to me with any markdown, just the raw array.
      Here is the creative brief:

      ${body.creativeBrief}
      make sure to return a pure array , NOT MARKDOWN!
      Evaluate if visual content would enhance the message and set hasVisualContent accordingly, and add a suggested visual description      Note, the tasks should be an actionable post. 
      ${TaskListSchema.describe('Array of marketing tasks with content details')}
    `;

    const completion = await openai.chat.completions.create({
      model: 'o1-mini-2024-09-12',
      messages: [{ role: 'user', content: prompt }],
    });

    const content = JSON.parse(
      JSON.stringify(completion?.choices[0]?.message),
    )?.content;


    const tasksWithDefaults: TaskList = content.map((task: Task) => ({
      ...task,
      is_scheduled: false,
      is_posted: false,
      created_at: currentTimestamp,
      scheduled_for: null,
      language: body.language || 'en',
    }));

    return new Response(JSON.stringify(tasksWithDefaults), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.log({ error });
    return new Response(null, { status: 500 });
  }
};
