import { NextRequest } from 'next/server';

import { getApi } from '~/utils/api.util';

/**
 * @name POST
 * @description Generate template channel content
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("BODY", body);
    const response = await getApi().post(
      '/sb_generate_template_channel_content_v4',
      body,
    );

    return new Response(JSON.stringify(response.data), { status: 200 });
  } catch (error) {
    return new Response(JSON.stringify(error), { status: 500 });
  }
}
