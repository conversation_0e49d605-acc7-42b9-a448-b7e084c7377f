/**
 * @name POST
 * @description POST handler
 */
import { NextRequest } from 'next/server';
import { getApi } from '~/utils/api.util';


interface BriefRequestBody {
  creativeBrief: string;
  language: string;
}

export const POST = async (request: NextRequest) => {
  try {
    const body = (await request.json()) as BriefRequestBody;

    console.log({ body });
    const res = await getApi().post('/sb_generate_creative_brief_tasks_v2', {
      creative_brief: JSON.stringify(body.creativeBrief),
      language: "English",
    });

    console.log(res.data);
    return new Response(JSON.stringify(res.data), { status: 200 });
  } catch (error) {
    // return an error response
    return new Response(JSON.stringify(error), { status: 500 });
  }
};
