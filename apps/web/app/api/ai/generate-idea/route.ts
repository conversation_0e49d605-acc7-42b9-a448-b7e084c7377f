import { NextRequest } from 'next/server';
import { getApi } from '~/utils/api.util';

interface GenerateIdeaRequestBody {
  campaign_brief: string;
  previous_ideas: string[];
  language: string;
  brand_brief: string;
  brand_name: string;
  product_info: string[];
}

/**
 * @name POST
 * @description POST handler
 */
export const POST = async (request: NextRequest) => {
  try {
    const body = (await request.json()) as GenerateIdeaRequestBody;

    const res = await getApi().post(
      '/sb_generate_new_idea_v2',
      JSON.stringify({
        campaign_brief: body.campaign_brief,
        previous_ideas: body.previous_ideas,
        brand_brief: body.brand_brief,
        brand_name: body.brand_name,
        language: body.language,
        product_info: body.product_info,
      }),
    );
    console.log(res.data);
    return new Response(JSON.stringify(res.data), { status: 200 });
  } catch (error) {
    // return an error response
    return new Response(JSON.stringify(error), { status: 500 });
  }
};
