import axios from 'axios';

/**
 * @name POST
 * @description POST handler
 */
export const POST = async (request: Request) => {
  try {
    const body = await request.json();
    console.log('generateVisualDescription - body', body);
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_PUSH_SERVER}/generate-visual-description`,
      {
        brand_brief: JSON.stringify(body.brand_brief)|| "NOT_PROVIDED",
        content: body.content|| "NOT_PROVIDED",
        initial_visual_desc: body.initial_visual_desc|| "NOT_PROVIDED",
        image_gen_styles: JSON.stringify(body.image_gen_styles)|| "NOT_PROVIDED"
      }
    );
    console.log('response', response.data.result);
    // Ensure we're returning the visual description from the response
    return new Response(response.data.result, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error generating visual description:', error);
    return new Response(JSON.stringify({ error: 'Failed to generate visual description' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};