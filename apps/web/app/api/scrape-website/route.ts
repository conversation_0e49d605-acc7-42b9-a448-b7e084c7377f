import { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

import axios from 'axios';
import * as cheerio from 'cheerio';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(req: NextRequest) {
  try {
    try {
      const client = getSupabaseServerClient();
      const { data, error } = await client.auth.getUser();
      console.log({ data, error });
      if (!data.user || error) {
        return NextResponse.json({ error: 'User not found' }, { status: 401 });
      }
      // Get the URLs to scrape from the request body
      const { url, all } = await req.json();

      if (!url) {
        return NextResponse.json({ error: 'URL is required' }, { status: 400 });
      }

      const response = await axios({
        url,
        method: 'GET',
        params: {
          url: 'https://api.zenrows.com/v1/',
          apikey: process.env.ZENROWS_API_KEY,
          js_render: 'true',
          premium_proxy: 'true',
        },
      });
      if (all) {
        return NextResponse.json({
          message: 'Site Scraped successfully',
          status: 200,
          text: response.data,
        });
      }
      const $ = cheerio.load(response.data);
      $('script, style, noscript, head, meta, link').remove();
      // Remove comments
      $('*')
        .contents()
        .each(function () {
          if (this.type === 'comment') {
            $(this).remove();
          }
        });

      const text = $('body').text();
      const cleanedText = text.replace(/\n/g, '').replace(/\s+/g, ' ').trim();
      return NextResponse.json({
        message: 'Site Scraped successfully',
        status: 200,
        text: cleanedText,
      });
    } catch (authError) {
      console.error('Authentication error:', authError);
      return NextResponse.json(
        { error: 'Authentication failed', code: 'AUTH_FAILED' },
        { status: 401 },
      );
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
