import axios from 'axios';

/**
 * @name POST
 * @description POST handler
 */
export const POST =  
  async ( request: any ) => {
    try {
      const body = await request.json();
      console.log(body)
      // console.log({
      //   "campaign_info": body.campaign_info,
      //   "idea": body.initial_idea
      // })
     
      const res = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/sb_idea_review`, JSON.stringify({
        "campaign_info": body.campaign_info,
        "current_idea": body.current_idea
    }), {
      headers: {
          'Content-Type': 'application/json',
      },
  })
    console.log("RESRODFN", res.data);
    return new Response(res.data.idea_review_result, { status: 200 });
 
    } catch (error) {
      // return an error response
      console.log({error})
      return new Response(null, { status: 500 });
    }
  }