'use client';

import { useState } from 'react';
import { useAyrsharePost } from './use-ayrshare-post';

interface AyrsharePostExampleProps {
  companyId: string;
}

/**
 * Example component demonstrating how to use the Ayrshare posting functionality
 * This is just an example - you can integrate this into your existing UI components
 */
export function AyrsharePostExample({ companyId }: AyrsharePostExampleProps) {
  const { postToLinkedIn, loading, error } = useAyrsharePost();
  const [postContent, setPostContent] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!postContent.trim()) {
      return;
    }

    try {
      const options = imageUrl ? { mediaUrls: [imageUrl] } : {};
      const result = await postToLinkedIn(postContent, companyId, options);
      
      if (result.success) {
        setSuccessMessage('Post successfully published to LinkedIn!');
        setPostContent('');
        setImageUrl('');
        
        // Log the LinkedIn post URL if available
        const linkedInPost = result.data?.postIds.find(p => p.platform === 'linkedin');
        if (linkedInPost?.postUrl) {
          console.log('LinkedIn Post URL:', linkedInPost.postUrl);
        }
      }
    } catch (err) {
      console.error('Failed to post to LinkedIn:', err);
    }
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Post to LinkedIn via Ayrshare</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="postContent" className="block text-sm font-medium text-gray-700 mb-2">
            Post Content
          </label>
          <textarea
            id="postContent"
            value={postContent}
            onChange={(e) => setPostContent(e.target.value)}
            placeholder="What would you like to share on LinkedIn?"
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-2">
            Image URL (optional)
          </label>
          <input
            id="imageUrl"
            type="url"
            value={imageUrl}
            onChange={(e) => setImageUrl(e.target.value)}
            placeholder="https://example.com/image.jpg"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <button
          type="submit"
          disabled={loading || !postContent.trim()}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {loading ? 'Posting...' : 'Post to LinkedIn'}
        </button>
      </form>

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error}
        </div>
      )}

      {successMessage && (
        <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          {successMessage}
        </div>
      )}
    </div>
  );
}

// Example of how to use the client utilities directly (without React hooks)
export async function postExample(companyId: string) {
  try {
    // Import the client utilities
    const { postToLinkedIn } = await import('./client');
    
    // Post to LinkedIn
    const result = await postToLinkedIn(
      "Hello LinkedIn! This post was made via Ayrshare API.",
      companyId,
      {
        mediaUrls: ["https://example.com/image.jpg"], // Optional
        shortenLinks: true, // Optional
        disableComments: false, // Optional
      }
    );

    console.log('Post successful:', result);
    return result;
  } catch (error) {
    console.error('Post failed:', error);
    throw error;
  }
} 