import type { AyrsharePostRequest, AyrsharePostResponse } from './types';

/**
 * Post content to LinkedIn via Ayrshare API
 * @param data - The post data
 * @returns Promise with the API response
 */
export async function postToAyrshare(data: AyrsharePostRequest): Promise<AyrsharePostResponse> {
  const response = await fetch('/api/integrations/ayrshare/post', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP Error: ${response.status}`);
  }

  return response.json();
}

/**
 * Post content to LinkedIn specifically via Ayrshare API
 * @param post - The post content
 * @param companyId - The company ID
 * @param options - Additional options
 * @returns Promise with the API response
 */
export async function postToLinkedIn(
  post: string,
  companyId: string,
  options: {
    mediaUrls?: string[];
    scheduleDate?: string;
    shortenLinks?: boolean;
    disableComments?: boolean;
    linkedInOptions?: {
      title?: string;
      description?: string;
    };
  } = {}
): Promise<AyrsharePostResponse> {
  return postToAyrshare({
    post,
    platforms: ['linkedin'],
    companyId,
    ...options,
  });
}

/**
 * Post content to Twitter specifically via Ayrshare API
 * @param post - The post content
 * @param companyId - The company ID
 * @param options - Additional options
 * @returns Promise with the API response
 */
export async function postToTwitter(
  post: string,
  companyId: string,
  options: {
    mediaUrls?: string[];
    scheduleDate?: string;
    shortenLinks?: boolean;
    disableComments?: boolean;
  } = {}
): Promise<AyrsharePostResponse> {
  return postToAyrshare({
    post,
    platforms: ['twitter'],
    companyId,
    ...options,
  });
} 