import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  
  try {
    // Get user from session
    const supabase = getSupabaseServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      logger.warn('Unauthorized request to Ayrshare post endpoint');
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    logger.info('Ayrshare post request started', { userId: user.id });

    // Parse request body
    const body = await request.json();
    const { 
      post, 
      platforms = ['linkedin'], 
      mediaUrls, 
      scheduleDate,
      shortenLinks = false,
      disableComments = false,
      linkedInOptions,
      companyId 
    } = body;
    console.log("body", body);
    if (!post) {
      return NextResponse.json({ success: false, error: 'Post content is required' }, { status: 400 });
    }

    if (!companyId) {
      return NextResponse.json({ success: false, error: 'Company ID is required' }, { status: 400 });
    }

    // Get user's Ayrshare profile using existing pattern
    const { data: ayrshareProfiles, error: profileError } = await (supabase as any)
      .from('ayrshare_user_profile')
      .select('*')
      .eq('user_id', user.id)
      .eq('company_id', companyId);

    if (profileError || !ayrshareProfiles || (ayrshareProfiles as any[]).length === 0) {
      logger.error('No Ayrshare profile found for user', { userId: user.id, companyId, error: profileError });
      return NextResponse.json({ 
        success: false, 
        error: 'Ayrshare profile not found. Please connect your Ayrshare account first.' 
      }, { status: 400 });
    }

    const ayrshareProfile = (ayrshareProfiles as any[])[0];

    if (!ayrshareProfile.profileKey) {
      logger.error('Missing profile key for Ayrshare profile', { userId: user.id, companyId });
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid Ayrshare profile configuration. Missing profile key.' 
      }, { status: 400 });
    }

    // Prepare Ayrshare API request
    const ayrsharePayload: any = {
      post,
      platforms
    };

    // Add optional parameters if provided
    if (mediaUrls && mediaUrls.length > 0) {
      ayrsharePayload.mediaUrls = mediaUrls;
    }

    if (scheduleDate) {
      ayrsharePayload.scheduleDate = scheduleDate;
    }

    if (shortenLinks) {
      ayrsharePayload.shortenLinks = shortenLinks;
    }

    if (disableComments) {
      ayrsharePayload.disableComments = disableComments;
    }

    if (linkedInOptions) {
      ayrsharePayload.linkedInOptions = linkedInOptions;
    }

    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`
    };

    // Add Profile-Key header for user-specific posting
    if (ayrshareProfile.profileKey) {
      headers['Profile-Key'] = ayrshareProfile.profileKey;
    }
    console.log("ayrsharePayload", ayrsharePayload);
    console.log('Making request to Ayrshare API', { 
      platforms, 
      hasMedia: !!(mediaUrls && mediaUrls.length > 0),
      isScheduled: !!scheduleDate 
    });

    // Make request to Ayrshare API
    const ayrshareResponse = await fetch('https://api.ayrshare.com/api/post', {
      method: 'POST',
      headers,
      body: JSON.stringify(ayrsharePayload)
    });

    const responseData = await ayrshareResponse.json();

    if (!ayrshareResponse.ok) {
      // Log detailed error information
      console.log('Ayrshare API request failed', { 
        status: ayrshareResponse.status, 
        response: responseData,
        requestPayload: ayrsharePayload,
        headers: { ...headers, 'Authorization': 'Bearer [REDACTED]' } // Hide API key
      });
      
      // Extract specific error details from posts array if available
      let specificErrors = [];
      if (responseData.posts && Array.isArray(responseData.posts)) {
        specificErrors = responseData.posts.map((post: any) => ({
          platform: post.platform,
          status: post.status,
          errors: post.errors || [],
          message: post.message
        }));
        console.log('Detailed Ayrshare post errors:', specificErrors);
      }
      
      // Log full response structure for debugging
      console.log('Full Ayrshare response:', JSON.stringify(responseData, null, 2));
      
      return NextResponse.json({
        success: false,
        error: 'Failed to publish post via Ayrshare',
        details: responseData,
        specificErrors
      }, { status: ayrshareResponse.status });
    }

    logger.info('Successfully posted via Ayrshare', { 
      ayrsharePostId: responseData.id,
      status: responseData.status 
    });

    // Extract post URLs from successful posts
    const postUrls: Record<string, string> = {};
    let primaryPostUrl = '';
    console.log("responseData", JSON.stringify(responseData, null, 2));
    
    // Handle the nested structure: responseData.posts[].postIds[]
    if (responseData.posts && Array.isArray(responseData.posts)) {
      responseData.posts.forEach((post: any) => {
        if (post.postIds && Array.isArray(post.postIds)) {
          post.postIds.forEach((postId: any) => {
            if (postId.status === 'success' && postId.postUrl) {
              postUrls[postId.platform] = postId.postUrl;
              // Use the first successful post URL as primary
              if (!primaryPostUrl) {
                primaryPostUrl = postId.postUrl;
              }
            }
          });
        }
      });
    }

    console.log('Extracted post URLs:', postUrls);
    console.log('Primary post URL:', primaryPostUrl);

    // Return success response with Ayrshare data and extracted URLs
    return NextResponse.json({
      success: true,
      data: responseData,
      postUrls,
      primaryPostUrl,
      message: 'Post successfully published via Ayrshare'
    });

  } catch (error: any) {
    console.log('Error in Ayrshare post API', { error: error.message, stack: error.stack });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
} 