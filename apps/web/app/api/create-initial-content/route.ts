import { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import axios from 'axios';
import { InitialCampaignResponse } from '~/types/Campaign';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const client = getSupabaseServerClient();
    const { data: authData, error: authError } = await client.auth.getSession();
    console.log('Authenticated user:', authData.session?.user);
    const user = authData.session?.user
    if (!user || authError) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }
    
    // Get request body
    const requestBody = await req.json();
    const { 
      company_id, 
      user_id, 
      campaign_name, 
      slug, 
      objective, 
      websiteUrl, 
      company_name 
    } = requestBody;

    // Validate required fields
    if (!company_id || !user_id || !campaign_name || !slug || !objective || !websiteUrl || !company_name) {
      return NextResponse.json({ 
        error: 'Missing required fields',
        details: 'All fields are required: company_id, user_id, campaign_name, slug, objective, websiteUrl, company_name'
      }, { status: 400 });
    }

    console.log('Request body:', requestBody);
    // Forward the request to the backend service
    try {
      const response = await axios.post<InitialCampaignResponse>(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/create-initial-content`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      console.log('Response:', response.data);
      // Return the response from the backend
      return NextResponse.json<InitialCampaignResponse>(response.data);
    } catch (apiError: any) {
      console.error('API Error:', apiError?.response?.data || apiError);
      return NextResponse.json(
        { 
          error: 'Failed to create initial content', 
          details: apiError?.response?.data || 'Unknown error' 
        }, 
        { status: apiError?.response?.status || 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}