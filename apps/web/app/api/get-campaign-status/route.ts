import { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import axios from 'axios';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const client = getSupabaseServerClient();
    const { data: authData, error: authError } = await client.auth.getUser();
    
    if (!authData.user || authError) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }
    
    // Get the campaign ID from the request body
    const { campaign_id } = await req.json();

    if (!campaign_id) {
      return NextResponse.json({ error: 'Campaign ID is required' }, { status: 400 });
    }

    // Forward the request to the backend service
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/get-campaign-status`,
        { campaign_id },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      // Return the response from the backend
      return NextResponse.json(response.data);
    } catch (apiError: any) {
      console.error('API Error:', apiError?.response?.data || apiError);
      return NextResponse.json(
        { 
          error: 'Failed to get campaign status', 
          details: apiError?.response?.data || 'Unknown error' 
        }, 
        { status: apiError?.response?.status || 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}