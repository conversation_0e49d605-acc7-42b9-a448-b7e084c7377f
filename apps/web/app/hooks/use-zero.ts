'use client';
import { useEffect, useMemo, useState } from 'react';
import { Zero } from '@rocicorp/zero';
import { createUseZero } from '@rocicorp/zero/react';
import { schema } from '../types/schema';
import { useUser, useUserSession } from '@kit/supabase/hooks/use-user';

import { createMutators } from '../../lib/mutators';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

// Create a typed useZero hook
export const useZero = createUseZero<typeof schema, ReturnType<typeof createMutators>>();

// Custom hook to create and manage Zero instance
export function useZeroInstance() {
  const workspace = useTeamAccountWorkspace();
  const userId = workspace.user?.id;
  // @ts-expect-error - session is there - we should fix this
  const access_token = workspace.session?.data?.session?.access_token;

  const z =
   useMemo(() => {
    return new Zero({
      userID: userId,
      auth: access_token,
      server: process.env.NEXT_PUBLIC_ZERO_SERVER,
      mutators: createMutators(),
      schema,
    });

  }, [userId, access_token])
  
  return z;
}
