'use server';

import { v4 as uuidv4 } from 'uuid';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { StorageFile } from '~/types/assets';
import { FontPreview } from '~/types/brand';
import {
  ProductDocument,
  ProductDocumentWithLink,
} from '~/types/product-document';

import { getFontFormat, getUniqueId } from './utils';

const supabase = getSupabaseServerClient();

type LoggerContext = {
  name: string;
  userId?: string;
  companyId?: string;
  folderName?: string;
  fileName?: string;
  filePath?: string;
};

export async function listAssetFolders(companyId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'list-asset-folders',
  };

  logger.info(ctx, 'Fetching asset folders...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    ctx.companyId = companyId;

    // Fetch asset folders
    const { data: assetFolders, error: assetError } = await supabase.storage
      .from('brand-assets')
      .list(`${companyId}/assets`);

    if (assetError) {
      logger.error(
        { ...ctx, error: assetError },
        'Failed to fetch asset folders',
      );
      throw assetError;
    }

    // Fetch logo files (not folders)
    const { data: logoFiles, error: logoError } = await supabase.storage
      .from('brand-assets')
      .list(`${companyId}/brand/logos`);

    if (logoError) {
      logger.error({ ...ctx, error: logoError }, 'Failed to fetch logo files');
      throw logoError;
    }

    // Fetch generated folders
    const { data: generatedFolders, error: generatedError } = await supabase.storage
      .from('generated')
      .list(`${companyId}`);

    if (generatedError) {
      logger.error({ ...ctx, error: generatedError }, 'Failed to fetch generated folders');
      throw generatedError;
    }

    // Process asset folders
    const processedAssetFolders = assetFolders
      .filter((item) => item.name !== '.folder')
      .map((folder) => ({
        name: folder.name,
        path: `assets/${folder.name}`,
        type: 'assets',
      }));

    // Process generated folders
    const processedGeneratedFolders = generatedFolders
      .filter((item) => item.name !== '.folder')
      .map((folder) => ({
        name: folder.name,
        path: folder.name,
        type: 'generated',
      }));

    console.log("processedGeneratedFolders", processedGeneratedFolders);
    // Only include logos folder if it contains files
    const hasLogoFiles = logoFiles && logoFiles.length > 0;
    const logoFolder = hasLogoFiles
      ? [
          {
            name: 'logos',
            path: 'brand/logos',
            type: 'logos',
          },
        ]
      : [];

    const result = [...processedAssetFolders, ...logoFolder, ...processedGeneratedFolders];
    logger.info(
      {
        ...ctx,
        assetFolderCount: processedAssetFolders.length,
        generatedFolderCount: processedGeneratedFolders.length,
        hasLogoFolder: hasLogoFiles,
      },
      'Successfully fetched all folders',
    );

    return result;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error listing asset folders');
    throw error;
  }
}

export async function createAssetFolder(folderName: string, companyId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'create-asset-folder',
    folderName,
  };

  logger.info(ctx, 'Creating asset folder...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    ctx.companyId = companyId;
    ctx.filePath = `${companyId}/assets/${folderName}/.folder`;

    const emptyFile = new Uint8Array(0);

    const { error } = await supabase.storage
      .from('brand-assets')
      .upload(ctx.filePath, emptyFile);

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to create asset folder');
      throw error;
    }

    logger.info(ctx, 'Successfully created asset folder');
    return folderName;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error creating asset folder');
    throw error;
  }
}

export async function listFolderContents(
  folderName: string,
  companyId: string,
): Promise<StorageFile[]> {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'list-folder-contents',
    folderName,
  };

  logger.info(ctx, 'Fetching folder contents...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    ctx.companyId = companyId;

    let path;
    let bucket = 'brand-assets';

    if (folderName) {
      if (folderName === 'logos') {
        path = `${companyId}/brand/${folderName}`;
      } else if (folderName.startsWith('generated/') || folderName === 'generated') {
        bucket = 'generated';
        path = `${companyId}/${folderName.replace('generated/', '')}`;
      } else {
        path = `${companyId}/assets/${folderName}`;
      }
    } else {
      path = `${companyId}/assets`;
    }

    ctx.filePath = path;

    const { data, error } = await supabase.storage
      .from(bucket)
      .list(path, {
        limit: 100,
        offset: 0,
        sortBy: { column: 'name', order: 'asc' },
      });

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to list folder contents');
      throw error;
    }

    // Filter out .folder files and process the rest
    const files = data.filter((item) => item.name !== '.folder');

    // Get all images from subfolders if no specific folder is provided
    const allFiles = await Promise.all(
      files.map(async (file) => {
        // If it's a folder and no specific folder was requested, get its contents
        if (!folderName && !file.name.includes('.')) {
          const subFolderFiles = await listFolderContents(file.name, companyId);
          return subFolderFiles;
        }

        let filePath;
        if (folderName) {
          if (folderName === 'logos') {
            filePath = `${companyId}/brand/${folderName}/${file.name}`;
          } else if (bucket === 'generated') {
            filePath = `${companyId}/${folderName.replace('generated/', '')}/${file.name}`;
          } else {
            filePath = `${companyId}/assets/${folderName}/${file.name}`;
          }
        } else {
          filePath = `${companyId}/assets/${file.name}`;
        }

        const {
          data: { publicUrl },
        } = supabase.storage.from(bucket).getPublicUrl(filePath);

        return {
          name: file.name,
          path: filePath,
          url: publicUrl,
          type: file.metadata?.mimetype,
        };
      }),
    );

    // Flatten the array and filter out any undefined values
    const result = allFiles.flat().filter(Boolean);
    logger.info(
      { ...ctx, fileCount: result.length },
      'Successfully fetched folder contents',
    );
    return result;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error listing folder contents');
    throw error;
  }
}

export async function uploadMultipleAssetFiles(
  folderName: string,
  files: File[],
  companyId: string,
): Promise<StorageFile[]> {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-multiple-asset-files',
    folderName,
    // fileCount: files.length,
  };

  logger.info(ctx, 'Uploading multiple asset files...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    ctx.companyId = companyId;

    const uploads = files.map(async (file) => {
      const fileName = file.name || 'file';
      const [baseName = 'file', extension = 'bin'] = fileName.split('.');
      const sanitizedName = baseName.toLowerCase().replace(/[^a-z0-9]/g, '-');
      const finalFileName = `${getUniqueId()}-${sanitizedName}.${extension}`;
      const filePath = `${companyId}/assets/${folderName}/${finalFileName}`;

      const uploadCtx = { ...ctx, fileName: finalFileName, filePath };
      logger.info(uploadCtx, `Uploading file ${finalFileName}`);

      const { error } = await supabase.storage
        .from('brand-assets')
        .upload(filePath, file);

      if (error) {
        logger.error(
          { ...uploadCtx, error },
          `Failed to upload ${finalFileName}`,
        );
        throw error;
      }

      const {
        data: { publicUrl },
      } = supabase.storage.from('brand-assets').getPublicUrl(filePath);

      logger.info(uploadCtx, `Successfully uploaded ${finalFileName}`);
      return {
        path: filePath,
        url: publicUrl,
        name: finalFileName,
        type: file.type,
      };
    });

    const result = await Promise.all(uploads);
    logger.info(
      { ...ctx, uploadedCount: result.length },
      'Successfully uploaded all asset files',
    );
    return result;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error uploading asset files');
    throw error;
  }
}

async function fetchBrandData(companyId: string, _ctx: LoggerContext) {
  const { data: brandData, error: brandError } = await supabase
    .from('company_brand')
    .select('*')
    .eq('company_id', companyId)
    .single();

  if (brandError) {
    console.log("BRAND ERROR", {brandError: brandError});
    // ctx.logger.error(
    //   { ...ctx, error: brandError },
    //   'Failed to fetch brand information',
    // );
    return null;
  }

  return brandData;
}

async function fetchLogos(companyId: string, _ctx: LoggerContext) {
  const { data: logos, error: logosError } = await supabase.storage
    .from('brand-assets')
    .list(`${companyId}/brand/logos`);

  if (logosError) {
    console.log("LOGOS ERROR", {logosError: logosError});
    // ctx.logger.error({ ...ctx, error: logosError }, 'Failed to fetch logos');
    return null;
  }

  const logosWithUrls = await Promise.all(
    logos.map(async (logo: any) => ({
      ...logo,
      url: supabase.storage
        .from('brand-assets')
        .getPublicUrl(`${companyId}/brand/logos/${logo.name}`).data.publicUrl,
    })),
  );

  return logosWithUrls;
}

export async function fetchFonts(companyId: string) {

  const { data: fonts, error: fontsError } = await supabase.storage
    .from('brand-assets')
    .list(`${companyId}/brand/fonts`);

  if (fontsError) {
    console.log("FONTS ERROR", {fontsError: fontsError});
    // ctx.logger.error({ ...ctx, error: fontsError }, 'Failed to fetch fonts');
    return null;
  }

  const fontsWithUrls = await Promise.all(
    fonts.map(async (font: any) => {
      // Extract the original font name part before the first hyphen
      // This preserves the original font family name
      const fullNameWithoutExt = font.name.split('.')[0];
      // Extract everything before the last hyphen (which is the unique ID)
      const fontFamily = fullNameWithoutExt.split('-').slice(0, -1).join('-');
      
      const url = supabase.storage
        .from('brand-assets')
        .getPublicUrl(`${companyId}/brand/fonts/${font.name}`).data.publicUrl;

      const fontCss = `
        @font-face {
          font-family: '${fontFamily}';
          src: url('${url}') format('${getFontFormat(font.name)}');
        }
      `;

      return {
        ...font,
        url,
        fontFamily,
        fontCss,
      };
    }),
  );

  return fontsWithUrls;
}

export async function getBrandInformation(companyId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'get-brand-information',
    companyId,
  };

  logger.info(ctx, 'Fetching brand information...');

  try {
    const brandData = await fetchBrandData(companyId, ctx);
    if (!brandData) return null;

    const logosWithUrls = await fetchLogos(companyId, ctx);
    const fontsWithUrls = await fetchFonts(companyId);

    logger.info(
      {
        ...ctx,
        hasBrand: !!brandData,
        logoCount: logosWithUrls?.length || 0,
        fontCount: fontsWithUrls?.length || 0,
      },
      'Successfully fetched brand information',
    );

    return {
      brand: brandData,
      logos: logosWithUrls,
      fonts: fontsWithUrls,
    };
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error fetching brand information',
    );
    throw error;
  }
}

export async function uploadBrandFiles(
  images: Array<{ file: File; preview: string; name: string }>,
  companyId: string,
) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-brand-files',
    // fileCount: images.length,
  };

  logger.info(ctx, 'Uploading brand logo files...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    // const companyId = await getCurrentUserCompanyId();
    ctx.companyId = companyId;

    const uploads = images.map(async (image) => {
      const fileExt = image.file.name.split('.').pop();
      const sanitizedName = image.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
      const fileName = `${sanitizedName}-${uuidv4()}.${fileExt}`;
      const filePath = `${companyId}/brand/logos/${fileName}`;

      const uploadCtx = { ...ctx, fileName, filePath };
      logger.info(uploadCtx, `Uploading logo ${fileName}`);

      const { error } = await supabase.storage
        .from('brand-assets')
        .upload(filePath, image.file);

      if (error) {
        logger.error(
          { ...uploadCtx, error },
          `Failed to upload logo ${fileName}`,
        );
        throw error;
      }

      logger.info(uploadCtx, `Successfully uploaded logo ${fileName}`);
      return filePath;
    });

    const result = await Promise.all(uploads);
    logger.info(
      { ...ctx, uploadedCount: result.length },
      'Successfully uploaded all brand logo files',
    );
    return result;
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error uploading brand logo files',
    );
    throw error;
  }
}

export async function uploadFontFiles(fonts: FontPreview[], companyId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-font-files',
    // fileCount: fonts.length,
  };

  logger.info(ctx, 'Uploading font files...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    // const companyId = await getCurrentUserCompanyId();
    ctx.companyId = companyId;

    const uploads = fonts.map(async (font) => {
      // Convert base64 URL to File object
      const response = await fetch(font.url);
      const blob = await response.blob();
      const file = new File([blob], font.name);

      const fileExt = font.name.split('.').pop();
      const sanitizedName = font.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
      const fileName = `${sanitizedName}-${uuidv4()}.${fileExt}`;
      const filePath = `${companyId}/brand/fonts/${fileName}`;

      const uploadCtx = { ...ctx, fileName, filePath };
      logger.info(uploadCtx, `Uploading font ${fileName}`);

      const { error } = await supabase.storage
        .from('brand-assets')
        .upload(filePath, file);

      if (error) {
        logger.error(
          { ...uploadCtx, error },
          `Failed to upload font ${fileName}`,
        );
        throw error;
      }

      logger.info(uploadCtx, `Successfully uploaded font ${fileName}`);
      return filePath;
    });

    const result = await Promise.all(uploads);
    logger.info(
      { ...ctx, uploadedCount: result.length },
      'Successfully uploaded all font files',
    );
    return result;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error uploading font files');
    throw error;
  }
}

export async function deleteAssetFile(folderName: string, fileName: string, companyId: string) {
  const logger = await getLogger();
  const ctx = {
    name: 'delete-asset-file',
    folderName,
    fileName,
  };

  logger.info(ctx, 'Deleting asset file...');
  const server = getSupabaseServerClient();

  console.log("folderName", folderName);
  try {
    let filePath;
    let bucket = 'brand-assets';

    if (folderName === 'logos') {
      filePath = `${companyId}/brand/logos/${fileName}`;
    } else if (folderName.startsWith('generated/') || folderName === 'generated') {
      bucket = 'generated';
      filePath = `${companyId}/${folderName.replace('generated/', '')}/${fileName}`;
    } else {
      filePath = `${companyId}/assets/${folderName}/${fileName}`;
    }

    console.log("filePath", filePath);
    const { error } = await server.storage
      .from(bucket)
      .remove([filePath]);

    if (error) {
      logger.error({ ...ctx, error, filePath, bucket }, 'Failed to delete asset file');
      throw error;
    }

    logger.info({ ...ctx, filePath, bucket }, 'Successfully deleted asset file');
    return true;
  } catch (err) {
    logger.error({ ...ctx, error: err }, 'Unexpected error deleting asset file');
    throw err;
  }
}

export async function getDocumentWithUrls(
  documents: ProductDocument[] | null,
): Promise<ProductDocumentWithLink[]> {
  if (!documents?.length) return [];

  return await Promise.all(
    documents.map(async (doc) => ({
      id: doc.id,
      company_id: doc.company_id,
      title: doc.title,
      created_at: doc.created_at,
      file_path: doc.file_path,
      content: doc.content,
      file_type: doc.file_type,
      url: supabase.storage
        .from('company-products')
        .getPublicUrl(doc?.file_path ?? '').data.publicUrl,
    })),
  );
}

export async function uploadEditedImage(
  file: File,
  companyId: string,
): Promise<StorageFile> {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-edited-image',
  };

  logger.info(ctx, 'Uploading edited image...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    ctx.companyId = companyId;

    // Use a default name if file.name is undefined
    const fileName = file.name || 'edited-image.png';
    const fileExt = fileName.split('.').pop() || 'png';
    const baseName = fileName.split('.')[0] || 'edited-image';
    const sanitizedName = baseName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const finalFileName = `${getUniqueId()}-${sanitizedName}.${fileExt}`;
    const filePath = `${companyId}/assets/Edited/${finalFileName}`;

    const uploadCtx = { ...ctx, fileName: finalFileName, filePath };
    logger.info(uploadCtx, `Uploading edited image ${finalFileName}`);

    // Create the Edited folder if it doesn't exist
    try {
      await createAssetFolder('Edited', companyId);
    } catch {
      // Ignore error if folder already exists
    }

    const { error } = await supabase.storage
      .from('brand-assets')
      .upload(filePath, file);

    if (error) {
      logger.error(
        { ...uploadCtx, error },
        `Failed to upload ${finalFileName}`,
      );
      throw error;
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from('brand-assets').getPublicUrl(filePath);

    logger.info(uploadCtx, `Successfully uploaded edited image ${finalFileName}`);
    return {
      path: filePath,
      url: publicUrl,
      name: finalFileName,
      type: file.type,
    };
  } catch (err) {
    logger.error({ ...ctx, error: err }, 'Unexpected error uploading edited image');
    throw err;
  }
}

export async function uploadVideoFile(
  file: File,
  companyId: string,
): Promise<StorageFile> {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-video-file',
  };

  logger.info(ctx, 'Uploading video file...');

  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      logger.error(ctx, 'User not authenticated');
      throw new Error('Not authenticated');
    }

    ctx.userId = user.id;
    ctx.companyId = companyId;

    // Use a default name if file.name is undefined
    const fileName = file.name || 'video.mp4';
    const fileExt = fileName.split('.').pop() || 'mp4';
    const baseName = fileName.split('.')[0] || 'video';
    const sanitizedName = baseName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const finalFileName = `${getUniqueId()}-${sanitizedName}.${fileExt}`;
    // Use the "Uploads" folder as requested
    const folderName = 'Uploads';
    const filePath = `${companyId}/assets/${folderName}/${finalFileName}`;

    const uploadCtx = { ...ctx, fileName: finalFileName, filePath };
    logger.info(uploadCtx, `Uploading video ${finalFileName}`);

    // Create the Uploads folder if it doesn't exist
    try {
      await createAssetFolder(folderName, companyId);
    } catch (folderError: any) {
      // Ignore error if folder already exists (e.g., duplicate key value violates unique constraint)
      // Check for specific Supabase error codes if possible, otherwise a general check
      if (
        folderError.message &&
        !folderError.message.includes('duplicate key value')
      ) {
        logger.warn(
          { ...uploadCtx, error: folderError },
          `Could not ensure ${folderName} folder exists, continuing upload attempt.`,
        );
      }
    }

    const { error } = await supabase.storage
      .from('brand-assets')
      .upload(filePath, file);

    if (error) {
      logger.error(
        { ...uploadCtx, error },
        `Failed to upload ${finalFileName}`,
      );
      throw error;
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from('brand-assets').getPublicUrl(filePath);

    logger.info(uploadCtx, `Successfully uploaded video ${finalFileName}`);
    return {
      path: filePath,
      url: publicUrl,
      name: finalFileName,
      type: file.type,
    };
  } catch (err) {
    logger.error({ ...ctx, error: err }, 'Unexpected error uploading video file');
    throw err;
  }
}
