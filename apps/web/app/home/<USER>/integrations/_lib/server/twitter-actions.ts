'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { revalidatePath } from 'next/cache';
import { getTwitterProfile, disconnectTwitter, storeTwitterOAuthState } from '../../../../../services/twitter';
import crypto from 'crypto';

/**
 * Generate a cryptographically secure random string for OAuth state
 * @returns Random string for state parameter
 */
function generateRandomState(): string {
  return crypto.randomBytes(32).toString('base64url');
}

/**
 * Generate a cryptographically secure random string for PKCE code verifier
 * @returns Random string for code_verifier parameter
 */
function generateCodeVerifier(): string {
  return crypto.randomBytes(43).toString('base64url');
}

/**
 * Prepare Twitter OAuth flow by generating and storing state and code verifier
 * 
 * @param userId The user ID to prepare OAuth for
 * @returns Object containing state and code_challenge for the authorization URL
 */
export async function prepareTwitterOAuth(userId: string) {
  try {
    // Generate the state and code verifier
    const state = generateRandomState();
    const codeVerifier = generateCodeVerifier();
    
    // Simple code challenge method (plain) for this example
    // In production, use S256 with proper SHA-256 hashing
    const codeChallenge = codeVerifier;
    
    // Get the Supabase client
    const supabase = getSupabaseServerClient();
    
    // First clean up any existing state for partial connections
    try {

      const { data } = await supabase
        .from('twitterState')
        .select('access_token')
        .eq('user_id', userId)
        .single();
      
      // If there's no access token, clean up the record
      if (!data || !data.access_token) {

        await supabase
          .from('twitterState')
          .delete()
          .eq('user_id', userId);
      }
    } catch (error) {
      console.error('no record exists:', error);
      // Ignore errors - just means no record exists
    }
    
    // Store the state and code verifier in the database
    const success = await storeTwitterOAuthState(userId, state, codeVerifier);
    
    if (!success) {
      return {
        success: false,
        error: 'Failed to store OAuth state'
      };
    }
    
    return {
      success: true,
      state,
      codeChallenge,
      codeChallengeMethod: 'plain' // Use 'S256' in production
    };
  } catch (error) {
    console.error('Error preparing Twitter OAuth:', error);
    return {
      success: false,
      error: 'Error preparing OAuth flow'
    };
  }
}

/**
 * Interface for Twitter OAuth data
 */
interface TwitterOAuthData {
  state: string;
  access_token: string;
  expires_in?: number;
  refresh_token?: string;
  refresh_token_expires_in?: number;
  scope?: string;
  user_id: string;
  screen_name?: string;
  name?: string;
  description?: string;
  profile_image_url?: string;
}

/**
 * Server action to store Twitter OAuth data in the database
 * This is used by the Twitter OAuth callback handler
 * 
 * @param data Twitter OAuth data to store
 * @returns Result of the operation
 */
export async function storeTwitterData(data: TwitterOAuthData) {
  try {
    const { 
      state, 
      access_token, 
      expires_in, 
      refresh_token, 
      refresh_token_expires_in, 
      scope,
      user_id,
      screen_name,
      name,
      description,
      profile_image_url
    } = data;

    // Validate required parameters
    if (!state || !access_token || !user_id) {
      return {
        success: false,
        error: 'Missing required parameters'
      };
    }

    // Get the Supabase client
    const supabase = getSupabaseServerClient();

    try {
      // Using upsert to handle both new connections and updates

      const { error: queryError } = await supabase
        .from('twitterState')
        .upsert({
          user_id,
          state, 
          access_token, 
          expires_in: expires_in ? new Date(Date.now() + expires_in * 1000).toISOString() : null, 
          refresh_token: refresh_token || null, 
          refresh_token_expires_in: refresh_token_expires_in 
            ? new Date(Date.now() + refresh_token_expires_in * 1000).toISOString() 
            : null, 
          scope: scope || null,
          screen_name: screen_name || null,
          name: name || null,
          description: description || null,
          profile_image_url: profile_image_url || null
        }, {
          onConflict: 'user_id'
        });
      
      if (queryError) {
        throw queryError;
      }
      
      // Revalidate the integrations page to show updated connection state
      revalidatePath('/home/<USER>/integrations');
      
      return { success: true };
    } catch (sqlError) {
      console.error('Error storing Twitter data:', sqlError);
      return {
        success: false,
        error: 'Database error',
        details: (sqlError as Error).message
      };
    }
  } catch (error) {
    console.error('Error processing Twitter data:', error);
    return {
      success: false,
      error: 'Internal server error'
    };
  }
}

/**
 * Get Twitter profile for the current user
 * 
 * @param userId User ID to get profile for
 * @returns Twitter profile data or null
 */
export async function getUserTwitterProfile(userId: string) {
  try {
    const profile = await getTwitterProfile(userId);
    
    return {
      success: true,
      profile
    };
  } catch (error) {
    console.error('Error fetching Twitter profile:', error);
    return {
      success: false,
      error: 'Failed to fetch Twitter profile'
    };
  }
}

/**
 * Disconnect Twitter for a user
 * 
 * @param userId User ID to disconnect
 * @returns Success or failure
 */
export async function disconnectUserTwitter(userId: string) {
  try {
    const success = await disconnectTwitter(userId);
    
    if (success) {
      // Revalidate the integrations page to show updated connection state
      revalidatePath('/home/<USER>/integrations');
      
      return {
        success: true
      };
    }
    
    return {
      success: false,
      error: 'Failed to disconnect Twitter'
    };
  } catch (error) {
    console.error('Error disconnecting Twitter:', error);
    return {
      success: false,
      error: 'Internal server error'
    };
  }
} 

interface RefreshTokenResponse {
  token_type: string;
  access_token: string;
  expires_in: number;
  scope: string;
  refresh_token?: string;
}

export async function refreshAccessToken({
  refreshToken,
  clientId,
  redirectUri,
}: {
  refreshToken: string;
  clientId: string;
  redirectUri: string;
}): Promise<RefreshTokenResponse> {
  const url = 'https://api.twitter.com/2/oauth2/token';

  const body = new URLSearchParams({
    grant_type: 'refresh_token',
    refresh_token: refreshToken,
    client_id: clientId,
    redirect_uri: redirectUri,
  });

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to refresh token: ${response.status} ${errorText}`);
  }

  const data: RefreshTokenResponse = await response.json();
  return data;
}