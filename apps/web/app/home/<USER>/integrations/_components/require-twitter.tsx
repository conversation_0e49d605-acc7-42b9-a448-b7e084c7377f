'use client';

import { createContext, useContext, useEffect, useState } from 'react';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { getUserTwitterProfile } from '../_lib/server/twitter-actions';
import { TwitterNotConnectedDialog } from '../../studio/components/content-studio-workspace/components/TwitterNotConnectedDialog';

// interface TwitterProfile {
//   screen_name: string | null;
//   name: string | null;
//   description: string | null;
//   profile_image_url: string | null;
//   access_token?: string;
// }

interface TwitterConnectionContextType {
  isConnected: boolean;
  isLoading: boolean;
  checkConnection: () => Promise<boolean>;
  showTwitterDialog: () => void;
}

const TwitterConnectionContext = createContext<TwitterConnectionContextType>({
  isConnected: false,
  isLoading: true,
  checkConnection: async () => false,
  showTwitterDialog: () => {},
});

export const useTwitterConnection = () => {
  return useContext(TwitterConnectionContext);
};

interface RequireTwitterProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function RequireTwitter({ children, fallback }: RequireTwitterProps) {
  const workspace = useTeamAccountWorkspace();
  const userId = workspace.user.id;
  const accountSlug = workspace.account.slug;
  const accountId = workspace.account.id;
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);

  const checkConnection = async (): Promise<boolean> => {
    if (!accountId) return false;

    try {
      const response = await getUserTwitterProfile(userId);
      const hasTwitterProfile = !!(response.success && response.profile);
      setIsConnected(hasTwitterProfile);
      return hasTwitterProfile;
    } catch (e) {
      console.log({e});
      setIsConnected(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const showTwitterDialog = () => {
    setShowDialog(true);
  };

  useEffect(() => {
    checkConnection();
  }, [accountId]);

  if (isLoading) {
    return null;
  }

  if (!isConnected) {
    return (
      <>
        <TwitterNotConnectedDialog
          open={showDialog}
          onOpenChange={setShowDialog}
          accountSlug={accountSlug}
        />
        {fallback}
      </>
    );
  }

  return (
    <TwitterConnectionContext.Provider
      value={{ isConnected, isLoading, checkConnection, showTwitterDialog }}
    >
      {children}
    </TwitterConnectionContext.Provider>
  );
} 