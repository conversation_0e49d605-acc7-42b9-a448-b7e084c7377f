'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Skeleton } from '@kit/ui/skeleton';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import { getUserTwitterProfile, disconnectUserTwitter, prepareTwitterOAuth } from '../_lib/server/twitter-actions';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';


interface TwitterProfile {
  screen_name: string | null;
  name: string | null;
  description: string | null;
  profile_image_url: string | null;
}

/**
 * Component for Twitter/X integration with OAuth flow
 */
export default function TwitterIntegrationCard() {
  const router = useRouter();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [profile, setProfile] = useState<TwitterProfile | null>(null);
  const [disconnecting, setDisconnecting] = useState(false);
  const { resolvedTheme } = useTheme();
  const workspace = useTeamAccountWorkspace();
  const userId = workspace.user.id;
  
  const logoSrc = resolvedTheme === 'dark' 
    ? '/images/logo-white.png' 
    : '/images/logo-black.png';

  // Add hook for URL search params to check for error messages
  useEffect(() => {
    // Check URL for any error parameters from OAuth callback
    const url = new URL(window.location.href);
    const error = url.searchParams.get('error');
    const errorCode = url.searchParams.get('code');
    const twitterConnected = url.searchParams.get('twitter_connected');

    if (error) {
      console.error(`Twitter OAuth error: ${error}`, errorCode ? `(code: ${errorCode})` : '');
    }

    if (twitterConnected === 'true') {
      // Refresh the profile data after successful connection
      fetchTwitterProfile();
    }
  }, []);

  const fetchTwitterProfile = async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      
      // Call the server action to get Twitter profile
      const response = await getUserTwitterProfile(userId);
      
      if (response.success && response.profile && response.profile.access_token) {
        setProfile(response.profile);
      } else {
        setProfile(null);
      }
    } catch (error) {
      console.error('Error fetching Twitter profile:', error);
      setProfile(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch current user's Twitter connection status
  useEffect(() => {
    fetchTwitterProfile();
  }, [userId]);

  const handleConnect = async () => {
    try {
      setIsConnecting(true);
      
      // Call server action to prepare OAuth flow
      // This will generate state and code verifier and store them in the database
      const response = await prepareTwitterOAuth(userId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to prepare OAuth flow');
      }
      
      const { state, codeChallenge, codeChallengeMethod } = response;
      
      // Define the scopes needed for the integration
      const scopes = [
        'tweet.read',
        'tweet.write',
        'users.read',
        'offline.access'
      ];
      
      const scope = encodeURIComponent(scopes.join(' '));
      
      // Get the client ID from environment variable
      const clientId = process.env.NEXT_PUBLIC_X_CLIENT_ID;
      console.log('clientId', clientId);
      
      if (!clientId) {
        throw new Error('Twitter client ID not configured');
      }
      
      // Encode redirect URI
      const redirectUri = encodeURIComponent(`${window.location.origin}/api/auth/twitter/callback`);
      
      // Construct the authorization URL with the state and code challenge from our server action
      const authUrl = `https://x.com/i/oauth2/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=${scope}&code_challenge=${codeChallenge}&code_challenge_method=${codeChallengeMethod}`;
      
      // Redirect the user to Twitter authorization page
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error connecting to Twitter:', error);
      setIsConnecting(false);
    }
  };

  // Handle disconnecting Twitter account
  const handleDisconnect = async () => {
    if (!userId) return;
    
    try {
      setDisconnecting(true);
      
      // Call the server action to disconnect Twitter
      const response = await disconnectUserTwitter(userId);
      
      if (response.success) {
        // Clear profile data on successful disconnect
        setProfile(null);
        // Refresh the UI
        router.refresh();
      } else {
        console.error('Failed to disconnect Twitter:', response.error);
      }
    } catch (error) {
      console.error('Error disconnecting Twitter:', error);
    } finally {
      setDisconnecting(false);
    }
  };

  // Render connected profile
  const renderConnectedProfile = () => {
    if (!profile) return null;
    
    return (
      <>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="flex mb-8 flex-row grow items-center justify-around space-y-0 pb-2">
              <img 
                src={logoSrc}
                alt="Twitter/X Logo" 
                width={40}
                height={40}
                className="object-contain" 
              />
            </CardTitle>
            <CardDescription className="flex items-center space-x-2">
              
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            {profile.profile_image_url ? (
              <div className="h-16 w-16 rounded-full overflow-hidden relative">
                <img 
                  src={profile.profile_image_url} 
                  alt={`${profile.name}'s profile`} 
                  className="object-cover" 
                />
              </div>
            ) : (
              <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xl">
                {profile.name?.[0] || '?'}
              </div>
            )}
            <div>
              <h3 className="font-medium text-lg">{profile.name}</h3>
              {profile.screen_name && (
                <p className="text-sm text-muted-foreground">@{profile.screen_name}</p>
              )}
              {profile.description && (
                <p className="text-sm text-muted-foreground mt-1">{profile.description}</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <>
            
          </>
          <Button 
            variant="outline" 
            className="text-red-500 justify-end align-end self-end hover:text-red-700 hover:bg-red-50"
            onClick={handleDisconnect}
            disabled={disconnecting}
          >
            {disconnecting ? 'Disconnecting...' : 'Disconnect'}
          </Button>
        </CardFooter>
      </>
    );
  };

  // Render connection form
  const renderConnectForm = () => (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>Twitter/X</CardTitle>
          <CardDescription>Connect your Twitter/X account</CardDescription>
        </div>
        <div className="h-12 w-12 relative">
          <Image 
            src={logoSrc}
            alt="Twitter/X Logo" 
            fill 
            className="object-contain" 
          />
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          Connect your Twitter/X account to post updates, engage with your audience, and track mentions.
        </p>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleConnect} 
          disabled={isConnecting}
        >
          {isConnecting ? 'Connecting...' : 'Connect'}
        </Button>
      </CardFooter>
    </>
  );

  // Render loading state
  const renderLoadingState = () => (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-2">
          <Skeleton className="h-5 w-[120px]" />
          <Skeleton className="h-4 w-[180px]" />
        </div>
        <Skeleton className="h-12 w-12" />
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-16 w-16 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-[150px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Skeleton className="h-9 w-[100px]" />
      </CardFooter>
    </>
  );

  return (
    <Card>
      {isLoading 
        ? renderLoadingState() 
        : profile 
          ? renderConnectedProfile() 
          : renderConnectForm()
      }
    </Card>
  );
} 