'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';
import { toast } from '@kit/ui/sonner';
import { MoreHorizontal, Share2, Users, Edit, Trash2, Lock } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { getSocialProfiles, getProfilesDetails, disconnectSocialProfile, deleteSocialProfile, generateJWT } from '../_lib/server/server-actions';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';

interface ProfileDetails {
  activeSocialAccounts: string[];
  displayNames: Array<{
    created: string;
    displayName: string;
    id: string;
    platform: string;
    profileUrl: string;
    userImage: string;
    username: string;
    headline?: string;
    subscriptionType?: string;
    verifiedType?: string;
    refreshDaysRemaining?: number;
    refreshRequired?: string;
    type?: string;
  }>;
  email: string | null;
  monthlyApiCalls: number;
  monthlyPostCount: number;
  refId: string;
  title: string;
  lastUpdated: string;
  nextUpdate: string;
}

const platformLogos = {
  linkedin: '/images/LI-Logo.png',
  twitter: '/images/logo-black.png',
  facebook: '/images/logo-black.png',
  instagram: '/images/logo-black.png',
};

const platformNames = {
  linkedin: 'LinkedIn',
  twitter: 'Twitter/X',
  facebook: 'Facebook',
  instagram: 'Instagram',
};

export function SocialProfilesList() {
  const workspace = useTeamAccountWorkspace();

  const [profileDetails, setProfileDetails] = useState<Record<string, ProfileDetails>>({});
  const [loading, setLoading] = useState(true);
  const [loadingDetails, setLoadingDetails] = useState<Record<string, boolean>>({});
  const [disconnecting, setDisconnecting] = useState<string | null>(null);
  const zero = useZero();

  const [profiles] = useZeroQuery(
    zero.query.ayrshare_user_profile,
    {
      ttl: '10m'
    }
  );

  const fetchProfiles = async () => {
    try {
      setLoading(true);
      // const profilesData = await getSocialProfiles(workspace.user.id, workspace.account.id);

      // console.log("profilesData", profilesData);
      // Fetch details for each profile
      if (profiles && profiles.length > 0) {
        const detailsPromises = profiles.map(async (profile: any) => {
          setLoadingDetails(prev => ({ ...prev, [profile.id]: true }));
          try {
            const details = await getProfilesDetails(profile.profileKey);

            zero.mutate.ayrshare_social_profiles.upsert({
              id: profile.id,
              values: {
                active_social_accounts: details.activeSocialAccounts,
                display_names: details.displayNames,
                refId: profile.refId,
                is_shared: profile.is_shared,
                user_id: profile.user_id,
                company_id: profile.company_id,
              }
            });
            return { profileId: profile.id, details };
          } catch (error) {
            console.error(`Error fetching details for profile ${profile.id}:`, error);
            return { profileId: profile.id, details: null };
          } finally {
            setLoadingDetails(prev => ({ ...prev, [profile.id]: false }));
          }
        });

        const results = await Promise.all(detailsPromises);
        const detailsMap = results.reduce((acc, { profileId, details }) => {
          if (details) acc[profileId] = details;
          return acc;
        }, {} as Record<string, ProfileDetails>);
        console.log("detailsMap", {detailsMap});
        setProfileDetails(detailsMap);
      }
    } catch (error) {
      console.error('Error fetching profiles:', error);
      toast.error('Failed to load social profiles');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfiles();
  }, [workspace.user.id, workspace.account.id]);

  const handleConnectAccounts = async (profile: any) => {

    if (!profile.profileKey) {
      toast.error('Profile key not found. Please contact support, or delete this profile and try again');
      return;
    }

    try {
      const jwtData = await generateJWT(profile.profileKey);

      if (jwtData.status === 'success') {
        window.open(jwtData.url, '_blank');
        toast.success('Opening social accounts connection page...');
      } else {
        toast.error('Failed to open connection page');
      }
    } catch (error) {
      console.error('Error generating JWT:', error);
      toast.error('Failed to open connection page');
    }
  };

  const handleDisconnectAccount = async (profile: any, platform: string) => {
    if (!profile.profileKey) {
      toast.error('Profile key not found');
      return;
    }

    setDisconnecting(`${profile.id}-${platform}`);
    try {
      await disconnectSocialProfile(platform, profile.profileKey);
      toast.success(`Disconnected ${platform} successfully`);
      // zero.mutate.ayrshare_user_profile.update({
      //   id: profile.id,
      //   values: {
    //     active_social_accounts: [],
      //     display_names: [],
      //   }
      // });
      // Refresh profile details
      // const details = await getProfilesDetails(profile.profileKey);
      // setProfileDetails(prev => ({ ...prev, [profile.id]: details }));
    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      toast.error(`Failed to disconnect ${platform}`);
    } finally {
      setDisconnecting(null);
    }
  };

  const handleSharedToggle = async (profile: any) => {
    console.log("profile", profile);
    zero.mutate.ayrshare_user_profile.update({
      id: profile.id,
      values: {
        is_shared: !profile.is_shared
      }
    });
    toast.success(`Profile ${profile.is_shared ? 'made private' : 'made shared'} successfully`);
  };

  const handleDeleteProfile = async (profile: any) => {
    if (!confirm(`Are you sure you want to delete the profile "${profile.title || 'Untitled'}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await deleteSocialProfile(profile.id);
      if (result.status === 'success') {
        toast.success('Profile deleted successfully');
        zero.mutate.ayrshare_user_profile.delete({
          id: profile.id,
        });
        // await fetchProfiles(); // Refresh the list
      } else {
        toast.error('Failed to delete profile');
      }
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast.error('Failed to delete profile');
    }
  };

  const renderConnectedAccount = (profile: any, account: ProfileDetails['displayNames'][0]) => {
    console.log("account", account, profile);
    const logoSrc = platformLogos[account.platform as keyof typeof platformLogos] || '/images/default-social-logo.png';
    const platformName = platformNames[account.platform as keyof typeof platformNames] || account.platform;
    const isDisconnecting = disconnecting === `${profile.id}-${account.platform}`;

    return (
      <div key={account.id} className="flex items-center justify-between p-3 border rounded-lg">
        <div className="flex items-center space-x-3">
          <img 
            src={logoSrc}
            alt={`${platformName} Logo`} 
            width={account.platform === 'linkedin' ? 32 : 24}
            height={account.platform === 'linkedin' ? 32 : 24}
            className="object-contain" 
          />
          
          {account.userImage && (
            <img 
              src={account.userImage} 
              alt={`${account.displayName}'s profile`} 
              className="w-8 h-8 rounded-full object-cover" 
            />
          )}
          
          <div>
            <p className="font-medium text-sm">{account.displayName}</p>
            {account.username && (
              <p className="text-xs text-muted-foreground">@{account.username}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="text-green-600 bg-green-50 text-xs">
            Connected
          </Badge>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => handleDisconnectAccount(profile, account.platform)}
            disabled={isDisconnecting}
            className="text-red-500 hover:text-red-700"
          >
            {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
          </Button>
        </div>
      </div>
    );
  };

  const renderProfileCard = (profile: any) => {
    const details = profileDetails[profile.id];
    const isLoadingDetails = loadingDetails[profile.id];
    const isOwner = profile.user_id === workspace.user.id;
    const connectedAccounts = details?.displayNames || [];

    return (
      <Card key={profile.id} className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>{profile.profile_name || 'Untitled Profile'}</span>
                {(profile.is_shared && isOwner ) ? (
                  <Badge variant="outline" className="text-xs">
                    <Share2 className="w-3 h-3 mr-1" />
                    Shared
                  </Badge>
                ) : 
                <Badge variant="outline" className="text-xs">
                  <Lock className="w-3 h-3 mr-1" />
                  Private
                </Badge>
              }
                {!isOwner && (
                  <Badge variant="secondary" className="text-xs">
                    <Users className="w-3 h-3 mr-1" />
                    Team Profile
                  </Badge>
                )}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Created {new Date(profile.created_at).toLocaleDateString()}
              </p>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleConnectAccounts(profile)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Manage Accounts
                </DropdownMenuItem>
              { isOwner && (
                <DropdownMenuItem onClick={() => handleSharedToggle(profile)}>
                  <Edit className="w-4 h-4 mr-2" />
                  {profile.is_shared ? 'Make Private' : 'Make Shared'}
                </DropdownMenuItem>
              )}
                {isOwner && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDeleteProfile(profile)}
                      className="text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Profile
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent>
          {isLoadingDetails ? (
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ) : connectedAccounts.length > 0 ? (
            <div className="space-y-3">
              <p className="text-sm font-medium">Connected Accounts ({connectedAccounts.length})</p>
              <div className="space-y-2">
                {connectedAccounts.map(account => renderConnectedAccount(profile, account))}
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-muted-foreground mb-4">No social accounts connected yet</p>
              <Button onClick={() => handleConnectAccounts(profile)}>
                Connect Social Accounts
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (profiles.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Share2 className="w-12 h-12 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">
          <Trans i18nKey="integrations:socialProfiles.noProfiles" defaults="No Social Profiles Yet" />
        </h3>
        <p className="text-muted-foreground mb-4">
          <Trans 
            i18nKey="integrations:socialProfiles.noProfilesDescription" 
            defaults="Create your first social profile to start connecting your social media accounts." 
          />
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          Your Social Profiles ({profiles.length})
        </h3>
      </div>
      
      <div className="space-y-4">
        {profiles.map(renderProfileCard)}
      </div>
    </div>
  );
} 