'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Skeleton } from '@kit/ui/skeleton';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { getUserLinkedInProfile, disconnectUserLinkedIn } from '../_lib/server/server-actions';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface LinkedInIntegrationCardProps {
  accountSlug: string;
}

interface LinkedInProfile {
  first_name: string | null;
  last_name: string | null;
  headline: string | null;
  profile_picture_url: string | null;
}

/**
 * Component for LinkedIn integration with OAuth flow
 */
export default function LinkedInIntegrationCard({ accountSlug }: LinkedInIntegrationCardProps) {
  const router = useRouter();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [profile, setProfile] = useState<LinkedInProfile | null>(null);
  const [disconnecting, setDisconnecting] = useState(false);
  const workspace = useTeamAccountWorkspace();
  console.log(workspace);
  const userId = workspace.user.id;
  // Generate a random string for state parameter
  const generateRandomState = () => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  };

  // Fetch current user's LinkedIn connection status
  useEffect(() => {
    const fetchLinkedInProfile = async () => {
      if (!userId) return;
      
      try {
        setIsLoading(true);
        
        // Call the server action to get LinkedIn profile
        const response = await getUserLinkedInProfile(userId);
        
        if (response.success && response.profile) {
          setProfile(response.profile);
        } else {
          setProfile(null);
        }
      } catch (error) {
        console.error('Error fetching LinkedIn profile:', error);
        setProfile(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLinkedInProfile();
  }, [userId]);

  const handleConnect = async () => {
    try {
      setIsConnecting(true);
      
      // Generate a secure random state parameter for CSRF protection
      const state = generateRandomState();
      
      // Store state in localStorage for verification when the user returns
      localStorage.setItem('linkedin_oauth_state', state);
      localStorage.setItem('linkedin_account_slug', accountSlug);
      
      // Updated scopes according to latest LinkedIn documentation
      // Use what your app needs and has been approved for - do not change this
      const scope = encodeURIComponent('r_basicprofile w_member_social w_organization_social r_organization_social');
      // Get the client ID
      const clientId = process.env.NEXT_PUBLIC_LINKED_IN_CLIENT_ID;
      
      if (!clientId) {
        throw new Error('LinkedIn client ID not configured');
      }
      
      // Encode redirect URI
      const redirectUri = encodeURIComponent(`${window.location.origin}/api/auth/linkedin/callback`);
      
      // Construct the authorization URL
      const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=${scope}`;
      
      // Redirect the user to LinkedIn authorization page
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error connecting to LinkedIn:', error);
      setIsConnecting(false);
    }
  };

  // Handle disconnecting LinkedIn account
  const handleDisconnect = async () => {
    if (!userId) return;
    
    try {
      setDisconnecting(true);
      
      // Call the server action to disconnect LinkedIn
      const response = await disconnectUserLinkedIn(userId);
      
      if (response.success) {
        // Clear profile data on successful disconnect
        setProfile(null);
        // Refresh the UI
        router.refresh();
      } else {
        console.error('Failed to disconnect LinkedIn:', response.error);
      }
    } catch (error) {
      console.error('Error disconnecting LinkedIn:', error);
    } finally {
      setDisconnecting(false);
    }
  };

  // Render connected profile
  const renderConnectedProfile = () => {
    if (!profile) return null;
    
    return (
      <>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="flex mb-8 flex-row grow items-center justify-around space-y-0 pb-2">
            <img 
              src="/images/LI-Logo.png" 
              alt="LinkedIn Logo" 
              width={125}
              height={125}
              className="object-contain" 
            />
            </CardTitle>
            <CardDescription className="flex items-center space-x-2">
              
            </CardDescription>
          </div>
         
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            {profile.profile_picture_url ? (
              <div className="h-16 w-16 rounded-full overflow-hidden relative">
                <img 
                  src={profile.profile_picture_url} 
                  alt={`${profile.first_name} ${profile.last_name}`} 
                  className="object-cover" 
                />
              </div>
            ) : (
              <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xl">
                {profile.first_name?.[0]}{profile.last_name?.[0]}
              </div>
            )}
            <div>
              <h3 className="font-medium text-lg">{profile.first_name} {profile.last_name}</h3>
              {profile.headline && (
                <p className="text-sm text-muted-foreground">{profile.headline}</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <>
            
          </>
          <Button 
            variant="outline" 
            className="text-red-500 justify-end align-end self-end hover:text-red-700 hover:bg-red-50"
            onClick={handleDisconnect}
            disabled={disconnecting}
          >
            {disconnecting ? 'Disconnecting...' : 'Disconnect'}
          </Button>
        </CardFooter>
      </>
    );
  };

  // Render connection form
  const renderConnectForm = () => (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>LinkedIn</CardTitle>
          <CardDescription>Connect your LinkedIn account</CardDescription>
        </div>
        <div className="h-12 w-24 relative">
          <Image 
            src="/images/LI-Logo.png" 
            alt="LinkedIn Logo" 
            fill 
            className="object-contain" 
          />
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          Connect your LinkedIn account to share updates and engage with your professional network.
        </p>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleConnect} 
          disabled={isConnecting}
        >
          {isConnecting ? 'Connecting...' : 'Connect'}
        </Button>
      </CardFooter>
    </>
  );

  // Render loading state
  const renderLoadingState = () => (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-2">
          <Skeleton className="h-5 w-[120px]" />
          <Skeleton className="h-4 w-[180px]" />
        </div>
        <Skeleton className="h-12 w-24" />
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-16 w-16 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-[150px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Skeleton className="h-9 w-[100px]" />
      </CardFooter>
    </>
  );

  return (
    <Card>
      {isLoading 
        ? renderLoadingState() 
        : profile 
          ? renderConnectedProfile() 
          : renderConnectForm()}
    </Card>
  );
} 