'use client';

import { createContext, useContext, useEffect, useState } from 'react';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { getUserLinkedInProfile } from '../_lib/server/server-actions';
import { LinkedInNotConnectedDialog } from '../../studio/components/content-studio-workspace/components/LinkedInNotConnectedDialog';

// interface LinkedInProfile {
//   first_name: string | null;
//   last_name: string | null;
//   headline: string | null;
//   profile_picture_url: string | null;
// }

interface LinkedInConnectionContextType {
  isConnected: boolean;
  isLoading: boolean;
  checkLinkedInConnection: () => Promise<boolean>;
  showLinkedInDialog: () => void;
}

const LinkedInConnectionContext = createContext<LinkedInConnectionContextType>({
  isConnected: false,
  isLoading: true,
  checkLinkedInConnection: async () => false,
  showLinkedInDialog: () => {},
});

export const useLinkedIn = () => {
  return useContext(LinkedInConnectionContext);
};

interface RequireLinkedInProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function RequireLinkedIn({ children }: RequireLinkedInProps) {
  const workspace = useTeamAccountWorkspace();
  const userId = workspace.user.id;
  const accountSlug = workspace.account.slug;
  const accountId = workspace.account.id;
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);

  const checkLinkedInConnection = async (): Promise<boolean> => {
    if (!accountId) return false;

    try {
      const response = await getUserLinkedInProfile(userId);
      // Convert to boolean value to fix type issues
      const hasLinkedInProfile = !!(response.success && response.profile);
      setIsConnected(hasLinkedInProfile);
      return hasLinkedInProfile;
    } catch (e) {
      console.log({e});
      setIsConnected(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const showLinkedInDialog = () => {
    setShowDialog(true);
  };

  useEffect(() => {
    checkLinkedInConnection();
  }, [accountId]);

  if (isLoading) {
    return null;
  }

  return (
    <LinkedInConnectionContext.Provider
      value={{ isConnected, isLoading, checkLinkedInConnection, showLinkedInDialog }}
    >
      {children}
      {showDialog && (
        <LinkedInNotConnectedDialog
          open={showDialog}
          onOpenChange={setShowDialog}
          accountSlug={accountSlug}
        />
      )}
    </LinkedInConnectionContext.Provider>
  );
} 