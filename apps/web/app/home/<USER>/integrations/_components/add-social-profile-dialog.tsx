'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Trans } from '@kit/ui/trans';
import { toast } from '@kit/ui/sonner';
import { Plus } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { createProfile, generateJWT } from '../_lib/server/server-actions';
import { useZero } from '~/hooks/use-zero';

const addProfileSchema = z.object({
  title: z.string().min(1, 'Profile title is required').max(100, 'Title must be less than 100 characters'),
});

type AddProfileFormData = z.infer<typeof addProfileSchema>;

interface AddSocialProfileDialogProps {
  onProfileAdded?: () => void;
}

export function AddSocialProfileDialog({ onProfileAdded }: AddSocialProfileDialogProps) {
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  const form = useForm<AddProfileFormData>({
    resolver: zodResolver(addProfileSchema),
    defaultValues: {
      title: '',
    },
  });

  const onSubmit = (data: AddProfileFormData) => {
    startTransition(async () => {
      try {
        const result = await createProfile(workspace.user.id, workspace.account.id, data.title);
        console.log("result", result);
        zero.mutate.ayrshare_user_profile.insert({
          id: crypto.randomUUID(),
          values: {
          title: result.title,
          refId: result.refId,
          profileKey: result.profileKey,
          messagingActive: result.messagingActive,
          user_id: workspace.user.id,
          company_id: workspace.account.id,
          profile_name: result.profileName
          }
        })

        if (result && result.status === 'success' && result.profileKey) {
          // Generate JWT using the profileKey from the created profile
          // const jwtData = await generateJWT(result.profileKey);

            // Open the social linking page in a new window
            // window.open(jwtData.url, '_blank');
            
            toast.success(
              <Trans 
                i18nKey="integrations:socialProfile.createSuccess" 
                defaults="Social profile created successfully! Connect your social accounts in the new window." 
              />
            );
            
            setOpen(false);
            form.reset();
            
          //   // Refresh the page to show the new profile in the list
          //   window.location.reload();
          // } else {
          //   toast.error(
          //     <Trans 
          //       i18nKey="integrations:socialProfile.jwtError" 
          //       defaults="Profile created but failed to open connection page. Please try again." 
          //     />
          //   );
          // }
        } else {
          toast.error(
            <Trans 
              i18nKey="integrations:socialProfile.createError" 
              defaults="Failed to create social profile. Please try again." 
            />
          );
        }
      } catch (error) {
        console.error('Error creating social profile:', error);
        toast.error(
          <Trans 
            i18nKey="integrations:socialProfile.unexpectedError" 
            defaults="An unexpected error occurred. Please try again." 
          />
        );
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          <Trans i18nKey="integrations:socialProfile.addNew" defaults="Add Social Profile" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="integrations:socialProfile.addTitle" defaults="Add New Social Profile" />
          </DialogTitle>
          <DialogDescription>
            <Trans 
              i18nKey="integrations:socialProfile.addDescription" 
              defaults="Create a new social media profile to manage different sets of social accounts (e.g., Personal, Company, Brand)." 
            />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="integrations:socialProfile.titleLabel" defaults="Profile Title" />
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Personal, Company, Brand A"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isPending}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <Trans i18nKey="integrations:socialProfile.creating" defaults="Creating..." />
                ) : (
                  <Trans i18nKey="integrations:socialProfile.create" defaults="Create Profile" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 