'use client';

import { useQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { If } from "@kit/ui/if";
import { Trans } from "@kit/ui/trans";
import { PageBody } from "@kit/ui/page";
import ICPsList from "./_components/icps-list";
import CreateICPDialog from "./_components/create-icp-dialog";
import EmptyICPs from "./_components/empty-icps";
import { GenerateICPDialog } from "./_components/generate-icp-dialog";
import { GenerateICPWithSocialProfileDialog } from "./_components/generate-icp-with-social-profile";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useState } from 'react';
import { Button } from '@kit/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { Plus, ChevronDown, Sparkles, Edit, Linkedin } from 'lucide-react';


export default function PersonasPage() {

  const { account } = useTeamAccountWorkspace();
  const [ isNew, setIsNew ] = useState(false);
  const [ isGenerateDialogOpen, setIsGenerateDialogOpen ] = useState(false);
  const [ isLinkedInDialogOpen, setIsLinkedInDialogOpen ] = useState(false);
  const accountSlug = account?.slug;
  const zero = useZero();
  const [icps] = useQuery(zero.query.icps.orderBy("created_at", "desc"), {
    ttl: '1d'
  });
  
  const [documents] = useQuery(zero.query.product_documents, {
    ttl: '1d'
  });

  const hasICPs = icps.length > 0;

return (
    <>
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold">
              <Trans i18nKey="personas:icpsTitle" defaults="Ideal Customer Profiles" />
            </h1>
            <p className="text-muted-foreground mt-2">
              <Trans i18nKey="personas:icpsDescription" defaults="Define your ideal customer profiles and organize your personas by customer segments" />
            </p>
          </div>
        </div>
      </div>

      <PageBody>
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">
          <Trans i18nKey="personas:yourICPs" defaults="Your Ideal Customer Profiles" />
        </h2>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              <Trans i18nKey="personas:createICP" defaults="Create ICP" />
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem className='cursor-pointer' onClick={() => setIsGenerateDialogOpen(true)}>
              <Sparkles className="h-4 w-4 mr-2" />
              <Trans i18nKey="personas:generateWithAI" defaults="Generate With AI" />
            </DropdownMenuItem>
            <DropdownMenuItem className='cursor-pointer' onClick={() => setIsLinkedInDialogOpen(true)}>
              <Linkedin className="h-4 w-4 mr-2" />
              <Trans i18nKey="personas:useLinkedInProfiles" defaults="Use LinkedIn Profiles" />
            </DropdownMenuItem>
            <DropdownMenuItem className='cursor-pointer' onClick={() => setIsNew(true)}>
              <Edit className="h-4 w-4 mr-2" />
              <Trans i18nKey="personas:createManually" defaults="Create Manually" />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
        <If condition={hasICPs}>
          {/* @ts-expect-error readonly */}
          <ICPsList icps={icps} accountSlug={accountSlug} />
        </If>
        
        <If condition={!hasICPs}>
          <EmptyICPs accountSlug={accountSlug} />
        </If>
        
        <CreateICPDialog isNew={isNew} setIsNew={setIsNew} companyId={account.id} />
        
        <GenerateICPDialog
          isOpen={isGenerateDialogOpen}
          onClose={() => setIsGenerateDialogOpen(false)}
          onGenerate={({ description, selectedDocuments }) => {
            zero.mutate.icps.insert({
              id: crypto.randomUUID(),
              values: {
                company_id: account.id,
                withAi: true,
                withLinkedIn: false,
                name: null,
                data: {},
                is_generating: true,
                error_generating: false,
                reference_material: selectedDocuments,
                reference_description: description
              }
            });
          }}
          documents={documents}
        />
        
        <GenerateICPWithSocialProfileDialog
          isOpen={isLinkedInDialogOpen}
          onClose={() => setIsLinkedInDialogOpen(false)}
        />
      </PageBody>
    </>
  );
}