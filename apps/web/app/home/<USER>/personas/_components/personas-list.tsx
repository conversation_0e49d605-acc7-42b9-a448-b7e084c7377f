'use client';

import { useState } from 'react';

import PersonaCard from './persona-card';
import PersonaListItem from './persona-list-item';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';

import { Grid, List, Search, Filter } from 'lucide-react';
import { Persona } from '~/types/persona';
import { ToggleGroup, ToggleGroupItem } from '@radix-ui/react-toggle-group';

interface PersonasListProps {
  personas: Persona[];
  accountSlug: string;
}

type ViewMode = 'grid' | 'list';
type SortOption = 'name' | 'role' | 'created_at' | 'updated_at';

export default function PersonasList({ personas, accountSlug }: PersonasListProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    department: 'all',
    managementLevel: 'all',
  });

  // Filter personas based on search query and filters
  const filteredPersonas = personas.filter(persona => {
    const personaData = persona.data;
    const matchesSearch = 
      searchQuery === '' || 
      personaData.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      personaData.role.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = 
      filters.status === 'all' || 
      (personaData.status || 'Active') === filters.status;
    
    const matchesDepartment = 
      filters.department === 'all' || 
      personaData.department === filters.department;
    
    const matchesManagementLevel = 
      filters.managementLevel === 'all' || 
      personaData.management_level === filters.managementLevel;
    
    return matchesSearch && matchesStatus && matchesDepartment && matchesManagementLevel;
  });

  // // Sort personas
  // const sortedPersonas = [...filteredPersonas].sort((a, b) => {
  //   switch (sortBy) {
  //     case 'name':
  //       return a.data.name.localeCompare(b.data.name);
  //     case 'role':
  //       return a.data.role.localeCompare(b.data.role);
  //     case 'created_at':
  //       return (b.created_at || 0) - (a.created_at || 0);
  //     case 'updated_at':
  //       return (b.updated_at || 0) - (a.updated_at || 0);
  //     default:
  //       return 0;
  //   }
  // });

  // Get unique departments and management levels for filters
  const departments = Array.from(new Set(personas.map(p => p.data.department).filter(Boolean)));
  const managementLevels = Array.from(new Set(personas.map(p => p.data.management_level).filter(Boolean)));

  return (
    <div className="space-y-4">
      {/* Uncomment and update these sections if you want to add filters back */}
      {/* <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search personas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-[200px] sm:w-[300px] pl-8"
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-muted' : ''}
          >
            <Filter className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="role">Role</SelectItem>
              <SelectItem value="created_at">Newest</SelectItem>
              <SelectItem value="updated_at">Recently Updated</SelectItem>
            </SelectContent>
          </Select>
          
          <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as ViewMode)}>
            <ToggleGroupItem value="grid" aria-label="Grid view">
              <Grid className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="List view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div> */}
      
      {showFilters && (
        <div className="p-4 bg-card rounded-md border flex flex-wrap gap-4">
          <div>
            <p className="text-sm font-medium mb-1">Status</p>
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters({...filters, status: value})}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {departments.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Department</p>
              <Select
                value={filters.department}
                onValueChange={(value) => setFilters({...filters, department: value})}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept as string}>{dept}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {managementLevels.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Level</p>
              <Select
                value={filters.managementLevel}
                onValueChange={(value) => setFilters({...filters, managementLevel: value})}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Management Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {managementLevels.map((level) => (
                    <SelectItem key={level} value={level as string}>{level}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          <Button 
            variant="outline" 
            className="self-end"
            onClick={() => setFilters({ status: 'all', department: 'all', managementLevel: 'all' })}
          >
            Reset Filters
          </Button>
        </div>
      )}
      
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPersonas.map((persona) => (
            <PersonaCard 
              key={persona.id} 
              id={persona.id} 
              persona={persona.data} 
              isGenerating={persona.is_generating} 
              errorGenerating={persona.error_generating} 
              accountSlug={accountSlug} 
            />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {filteredPersonas.map((persona) => (
            <PersonaListItem 
              key={persona.id} 
              id={persona.id} 
              persona={persona.data} 
              isGenerating={persona.is_generating} 
              errorGenerating={persona.error_generating} 
              accountSlug={accountSlug} 
            />
          ))}
        </div>
      )}
    </div>
  );
}