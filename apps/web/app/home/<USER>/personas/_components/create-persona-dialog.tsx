'use client';

import { useState, useTransition, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@kit/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';
import { Plus, X, User, Building, Crown, MapPin, Code, DollarSign, Target, Lightbulb, Users, Clock, Info, FileText, MessageSquare, AlignLeft, Tv, Hash, StickyNote } from 'lucide-react';


import { MultiSelect } from './multi-select';
import { createPersona } from '~/services/persona';
import { CustomField } from '../_lib/schema/icp.schema';
import CustomFieldDialog from './custom-field-dialog';
import NumberRange<PERSON>ield from './number-range-field';
import TagIn<PERSON>Field from './tag-input-field';
import { useZero } from '~/hooks/use-zero';

const managementLevels = [
  { label: 'C-level', value: 'C-level' },
  { label: 'VP-level', value: 'VP-level' },
  { label: 'Director', value: 'Director' },
  { label: 'Manager', value: 'Manager' },
  { label: 'Individual Contributor', value: 'Individual Contributor' },
];

const companySize = [
  { label: '1-10 employees', value: '1-10' },
  { label: '11-50 employees', value: '11-50' },
  { label: '51-200 employees', value: '51-200' },
  { label: '201-500 employees', value: '201-500' },
  { label: '501-1000 employees', value: '501-1000' },
  { label: '1001-5000 employees', value: '1001-5000' },
  { label: '5001+ employees', value: '5001+' },
];

const decisionMakingAuthority = [
  { label: 'Final decision maker', value: 'Final decision maker' },
  { label: 'Significant influence', value: 'Significant influence' },
  { label: 'Some influence', value: 'Some influence' },
  { label: 'Researcher only', value: 'Researcher only' },
];

const buyingStage = [
  { label: 'Awareness', value: 'Awareness' },
  { label: 'Consideration', value: 'Consideration' },
  { label: 'Decision', value: 'Decision' },
  { label: 'Implementation', value: 'Implementation' },
];

const communicationStyle = [
  { label: 'Technical', value: 'Technical' },
  { label: 'Business-focused', value: 'Business-focused' },
  { label: 'Conversational', value: 'Conversational' },
  { label: 'Formal', value: 'Formal' },
  { label: 'Visual/Graphics', value: 'Visual/Graphics' },
];

const contentLength = [
  { label: 'Short-form', value: 'Short-form' },
  { label: 'Medium', value: 'Medium' },
  { label: 'Long-form/Detailed', value: 'Long-form/Detailed' },
];

type FieldKey = 'role' | 'department' | 'management_level' | 'status' | 'industries' | 'company_size' | 'location' | 'tech_stack' | 'budget_range' | 'challenges' | 'goals' | 'decision_authority' | 'buying_stage' | 'info_preferences' | 'content_formats' | 'communication_style' | 'content_length' | 'channels' | 'topics' | 'notes';

interface FieldDefinition {
  key: FieldKey;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: 'Input' | 'MultiSelect' | 'Select' | 'Textarea';
  options?: { label: string; value: string }[];
  placeholder?: string;
}

const AVAILABLE_FIELDS: FieldDefinition[] = [
  {
    key: 'role',
    label: 'Job Title/Role',
    icon: User,
    description: 'Professional role or title',
    component: 'Input',
    placeholder: 'e.g., CTO, IT Director'
  },
  {
    key: 'department',
    label: 'Department',
    icon: Building,
    description: 'Department or team',
    component: 'Input',
    placeholder: 'e.g., IT, Marketing'
  },
  {
    key: 'management_level',
    label: 'Management Level',
    icon: Crown,
    description: 'Level in organization',
    component: 'Select',
    options: managementLevels,
    placeholder: 'Select management level'
  },
  {
    key: 'status',
    label: 'Status',
    icon: Target,
    description: 'Persona status',
    component: 'Select',
    options: [
      { label: 'Active', value: 'Active' },
      { label: 'Inactive', value: 'Inactive' },
      { label: 'Archived', value: 'Archived' }
    ],
    placeholder: 'Select status'
  },
  {
    key: 'industries',
    label: 'Industries',
    icon: Building,
    description: 'Target industry sectors',
    component: 'MultiSelect',
    placeholder: 'Select or add industry sectors'
  },
  {
    key: 'company_size',
    label: 'Company Size',
    icon: Users,
    description: 'Organization size',
    component: 'Select',
    options: companySize,
    placeholder: 'Select company size'
  },
  {
    key: 'location',
    label: 'Location',
    icon: MapPin,
    description: 'Geographic location',
    component: 'Input',
    placeholder: 'e.g., North America, Global'
  },
  {
    key: 'tech_stack',
    label: 'Technology Stack',
    icon: Code,
    description: 'Technologies they use',
    component: 'MultiSelect',
    placeholder: 'Select or add technologies'
  },
  {
    key: 'budget_range',
    label: 'Budget Range',
    icon: DollarSign,
    description: 'Available budget',
    component: 'Input',
    placeholder: 'e.g., $10k-$50k annually'
  },
  {
    key: 'challenges',
    label: 'Primary Challenges',
    icon: Target,
    description: 'Key pain points',
    component: 'MultiSelect',
    placeholder: 'Select or add key challenges'
  },
  {
    key: 'goals',
    label: 'Goals and Objectives',
    icon: Lightbulb,
    description: 'What they want to achieve',
    component: 'MultiSelect',
    placeholder: 'Select or add key goals'
  },
  {
    key: 'decision_authority',
    label: 'Decision-Making Authority',
    icon: Crown,
    description: 'Level of decision power',
    component: 'Select',
    options: decisionMakingAuthority,
    placeholder: 'Select decision-making role'
  },
  {
    key: 'buying_stage',
    label: 'Typical Buying Stage',
    icon: Clock,
    description: 'Where they are in buying process',
    component: 'Select',
    options: buyingStage,
    placeholder: 'Select buying stage'
  },
  {
    key: 'info_preferences',
    label: 'Information Preferences',
    icon: Info,
    description: 'How they like to consume info',
    component: 'MultiSelect',
    placeholder: 'Select or add information preferences'
  },
  {
    key: 'content_formats',
    label: 'Preferred Content Formats',
    icon: FileText,
    description: 'Content types they prefer',
    component: 'MultiSelect',
    placeholder: 'Select or add preferred formats'
  },
  {
    key: 'communication_style',
    label: 'Communication Style',
    icon: MessageSquare,
    description: 'How they like to communicate',
    component: 'Select',
    options: communicationStyle,
    placeholder: 'Select communication style'
  },
  {
    key: 'content_length',
    label: 'Content Length Preference',
    icon: AlignLeft,
    description: 'Preferred content length',
    component: 'Select',
    options: contentLength,
    placeholder: 'Select content length'
  },
  {
    key: 'channels',
    label: 'Preferred Channels',
    icon: Tv,
    description: 'Communication channels',
    component: 'MultiSelect',
    placeholder: 'Select or add preferred channels'
  },
  {
    key: 'topics',
    label: 'Topics of Interest',
    icon: Hash,
    description: 'What interests them',
    component: 'MultiSelect',
    placeholder: 'Select or add topics of interest'
  },
  {
    key: 'notes',
    label: 'Notes',
    icon: StickyNote,
    description: 'Additional information',
    component: 'Textarea',
    placeholder: 'Add any additional notes...'
  }
];

interface CreatePersonaDialogProps {
  isNew: boolean;
  companyId: string;
  setIsNew: (isNew: boolean) => void;
  icpId: string;
}

export default function CreatePersonaDialog({ isNew, setIsNew, companyId, icpId }: CreatePersonaDialogProps) {
  const [activeFields, setActiveFields] = useState<Set<FieldKey>>(new Set());
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [pending, startTransition] = useTransition();
  const zero = useZero();

  const form = useForm<any>({
    defaultValues: {
      name: '',
      status: 'Active',
      custom_fields: [],
    },
  });

  const addField = (fieldKey: FieldKey) => {
    setActiveFields(prev => new Set([...prev, fieldKey]));
    
    // Set default values for the field
    const field = AVAILABLE_FIELDS.find(f => f.key === fieldKey);
    if (field) {
      if (field.component === 'MultiSelect') {
        form.setValue(fieldKey, []);
      } else if (field.component === 'Select') {
        form.setValue(fieldKey, '');
      } else if (field.component === 'Input' || field.component === 'Textarea') {
        form.setValue(fieldKey, '');
      }
    }
  };

  const removeField = (fieldKey: FieldKey) => {
    setActiveFields(prev => {
      const newSet = new Set(prev);
      newSet.delete(fieldKey);
      return newSet;
    });
    form.setValue(fieldKey, undefined);
  };

  const addCustomField = (field: CustomField) => {
    setCustomFields(prev => [...prev, field]);
    // Update form with custom fields
    form.setValue('custom_fields', [...customFields, field]);
  };

  const removeCustomField = (fieldId: string) => {
    setCustomFields(prev => prev.filter(f => f.id !== fieldId));
    const updatedFields = customFields.filter(f => f.id !== fieldId);
    form.setValue('custom_fields', updatedFields);
  };

  const updateCustomFieldValue = (fieldId: string, value: any) => {
    setCustomFields(prev => prev.map(f => 
      f.id === fieldId ? { ...f, value } : f
    ));
    const updatedFields = customFields.map(f => 
      f.id === fieldId ? { ...f, value } : f
    );
    form.setValue('custom_fields', updatedFields);
  };

  const onSubmit = async (data: any) => {
    startTransition(async () => {
      try {
        // setActiveFields(new Set());
        // setCustomFields([]);
        // id, 
        // company_id,
        // icp_id,
        // withAi,
        // name,
        // data = {},
        // error_generating = false
        zero.mutate.personas.insert({
          id: crypto.randomUUID(),
          company_id: companyId,
          icp_id: icpId,
          withAi: false,
          name: data.name,
          data: data,
          error_generating: false,
        });
        setIsNew(false);
      } catch (error) {
        console.error('Failed to create persona:', error);
      }
    });
  };

  const renderField = (fieldDef: FieldDefinition) => {
    const { key, label, component, options, placeholder } = fieldDef;

    return (
      <FormField
        key={key}
        name={key}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center justify-between">
              <span>{label}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeField(key)}
                className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </Button>
            </FormLabel>
            <FormControl>
              {(() => {
                switch (component) {
                  case 'Input':
                    return (
                      <Input
                        placeholder={placeholder}
                        {...field}
                        value={field.value || ''}
                      />
                    );
                  case 'Textarea':
                    return (
                      <Textarea
                        placeholder={placeholder}
                        className="resize-none"
                        rows={4}
                        {...field}
                        value={field.value || ''}
                      />
                    );
                  case 'Select':
                    return (
                      <Select value={field.value || ''} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder={placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                          {options?.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    );
                  case 'MultiSelect':
                    return (
                      <MultiSelect
                        placeholder={placeholder || 'Select options'}
                        selected={field.value || []}
                        options={getMultiSelectOptions(key)}
                        onChange={field.onChange}
                        creatable
                      />
                    );
                  default:
                    return null;
                }
              })()}
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  const renderCustomField = (field: CustomField) => {
    const { id, label, type, value, options } = field;

    return (
      <div key={id} className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">{label}</label>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => removeCustomField(id)}
            className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        
        <div>
          {(() => {
            switch (type) {
              case 'text':
                return (
                  <Input
                    value={value || ''}
                    onChange={(e) => updateCustomFieldValue(id, e.target.value)}
                    placeholder={`Enter ${label.toLowerCase()}`}
                  />
                );
              case 'textarea':
                return (
                  <Textarea
                    value={value || ''}
                    onChange={(e) => updateCustomFieldValue(id, e.target.value)}
                    placeholder={`Enter ${label.toLowerCase()}`}
                    rows={3}
                    className="resize-none"
                  />
                );
              case 'number':
                return (
                  <Input
                    type="number"
                    value={value || ''}
                    onChange={(e) => updateCustomFieldValue(id, e.target.value ? Number(e.target.value) : null)}
                    placeholder={`Enter ${label.toLowerCase()}`}
                  />
                );
              case 'numberrange':
                return (
                  <NumberRangeField
                    value={value || { min: null, max: null }}
                    onChange={(val) => updateCustomFieldValue(id, val)}
                    placeholder={{ min: "Min", max: "Max" }}
                  />
                );
              case 'select':
                return (
                  <Select value={value || ''} onValueChange={(val) => updateCustomFieldValue(id, val)}>
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${label.toLowerCase()}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {options?.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                );
              case 'multiselect':
                return (
                  <TagInputField
                    value={value || []}
                    onChange={(val) => updateCustomFieldValue(id, val)}
                    placeholder={`Add ${label.toLowerCase()} options`}
                  />
                );
              default:
                return null;
            }
          })()}
        </div>
      </div>
    );
  };

  const getMultiSelectOptions = (fieldKey: FieldKey) => {
    switch (fieldKey) {
      case 'industries':
        return [
          'Technology', 'Finance', 'Healthcare', 'Education', 
          'Manufacturing', 'Retail', 'Media', 'Government'
        ].map(i => ({ label: i, value: i }));
      case 'tech_stack':
        return [
          'AWS', 'Azure', 'Google Cloud', 'React', 'Node.js',
          'Python', 'Java', 'Kubernetes', 'Docker'
        ].map(t => ({ label: t, value: t }));
      case 'challenges':
        return [
          'Cost management', 'Scalability', 'Integration', 
          'Security', 'Compliance', 'User adoption', 'Technical debt'
        ].map(c => ({ label: c, value: c }));
      case 'goals':
        return [
          'Reduce costs', 'Increase efficiency', 'Improve security',
          'Digital transformation', 'Revenue growth', 'Innovation'
        ].map(g => ({ label: g, value: g }));
      case 'info_preferences':
        return [
          'Technical details', 'Case studies', 'ROI analysis',
          'How-to guides', 'Industry research', 'Peer reviews', 'Competitive comparisons'
        ].map(p => ({ label: p, value: p }));
      case 'content_formats':
        return [
          'Blog posts', 'White papers', 'Webinars', 'Videos',
          'Infographics', 'Case studies', 'Podcasts', 'Demos'
        ].map(f => ({ label: f, value: f }));
      case 'channels':
        return [
          'Email', 'LinkedIn', 'Twitter', 'Industry events',
          'Webinars', 'Direct mail', 'Website', 'Search'
        ].map(c => ({ label: c, value: c }));
      case 'topics':
        return [
          'Digital transformation', 'Cloud migration', 'Security', 
          'AI/ML', 'Automation', 'Cost optimization', 'Compliance'
        ].map(t => ({ label: t, value: t }));
      default:
        return [];
    }
  };

  const availableFields = AVAILABLE_FIELDS.filter(field => !activeFields.has(field.key));

  return (
    <Dialog open={isNew} onOpenChange={setIsNew}>
      <DialogTrigger asChild>
        <Button className="fixed bottom-8 right-8 h-14 w-14 rounded-full shadow-lg">
          <Plus className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Persona</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Persona Name - Always visible */}
            <FormField
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Persona Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Technical Decision Maker" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Dynamic Fields */}
            {Array.from(activeFields).map(fieldKey => {
              const fieldDef = AVAILABLE_FIELDS.find(f => f.key === fieldKey);
              return fieldDef ? renderField(fieldDef) : null;
            })}

            {/* Custom Fields */}
            {customFields.map((field) => renderCustomField(field))}

            {/* Add Field Buttons */}
            {(availableFields.length > 0 || customFields.length >= 0) && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-muted-foreground">Add Fields</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {availableFields.map((field) => {
                    const Icon = field.icon;
                    return (
                      <Button
                        key={field.key}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addField(field.key)}
                        className="h-auto p-3 flex flex-col gap-1 text-left"
                      >
                        <div className="flex items-center gap-2 w-full">
                          <Icon className="h-4 w-4 flex-shrink-0" />
                          <span className="text-xs font-medium truncate">{field.label}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{field.description}</span>
                      </Button>
                    );
                  })}
                  
                  {/* Custom Field Button */}
                  <CustomFieldDialog onAddField={addCustomField} />
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-6">
              <Button type="button" variant="outline" onClick={() => setIsNew?.(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={pending}>
                {pending ? 'Creating...' : 'Create Persona'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}