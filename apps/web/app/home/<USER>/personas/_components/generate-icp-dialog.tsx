'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Sparkles, Check, ChevronsUpDown } from 'lucide-react';

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@kit/ui/form';
import { Textarea } from '@kit/ui/textarea';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@kit/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

interface ProductDocument {
  id: string;
  title: string;
  file_type: string;
}

interface GenerateICPDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (data: { description: string; selectedDocuments: string[] }) => void;
  documents: ProductDocument[];
  isGenerating?: boolean;
}

const GenerateICPSchema = z.object({
  description: z.string().min(1, 'Description is required').max(500, 'Description is too long'),
  selectedDocuments: z.array(z.string()).min(1, 'Please select at least one document'),
});

type GenerateICPFormData = z.infer<typeof GenerateICPSchema>;

export function GenerateICPDialog({
  isOpen,
  onClose,
  onGenerate,
  documents,
  isGenerating = false
}: GenerateICPDialogProps) {
  const [isDocumentSelectorOpen, setIsDocumentSelectorOpen] = useState(false);

  const form = useForm<GenerateICPFormData>({
    resolver: zodResolver(GenerateICPSchema),
    defaultValues: {
      description: '',
      selectedDocuments: [],
    },
  });

  const handleSubmit = (data: GenerateICPFormData) => {
    onGenerate(data);
    form.reset();
    onClose();
  };

  const handleClose = () => {
    if (!isGenerating) {
      form.reset();
      onClose();
    }
  };

  const selectedDocuments = form.watch('selectedDocuments');
  const selectedDocumentTitles = documents
    .filter(doc => selectedDocuments.includes(doc.id))
    .map(doc => doc.title);

  const toggleDocument = (documentId: string) => {
    const current = form.getValues('selectedDocuments');
    const updated = current.includes(documentId)
      ? current.filter(id => id !== documentId)
      : [...current, documentId];
    form.setValue('selectedDocuments', updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            <Trans i18nKey="personas:generateICPWithAI" defaults="Generate ICP with AI" />
          </DialogTitle>
          <DialogDescription>
            <Trans 
              i18nKey="personas:generateICPDescription" 
              defaults="Provide a brief description of your ideal customer and select relevant product documentation to help AI generate a comprehensive ICP."
            />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="personas:icpDescription" defaults="ICP Description" />
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your ideal customer in a few words..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans 
                      i18nKey="personas:icpDescriptionHelp" 
                      defaults="Describe in a few words if you have an idea about the ICP (e.g., 'Mid-size SaaS companies looking for customer analytics tools')"
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="selectedDocuments"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="personas:referenceDocuments" defaults="Reference Documents" />
                  </FormLabel>
                  <Popover open={isDocumentSelectorOpen} onOpenChange={setIsDocumentSelectorOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          type="button"
                          className={cn(
                            "w-full justify-between text-left font-normal",
                            !selectedDocuments.length && "text-muted-foreground"
                          )}
                        >
                          {selectedDocuments.length > 0
                            ? `${selectedDocuments.length} document(s) selected`
                            : "Select documents..."
                          }
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search documents..." />
                        <CommandList>
                          <CommandEmpty>No documents found.</CommandEmpty>
                          <CommandGroup>
                            {documents.map((document) => (
                              <CommandItem
                                key={document.id}
                                value={document.title}
                                onSelect={() => toggleDocument(document.id)}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedDocuments.includes(document.id)
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {document.title}
                                <Badge variant="secondary" className="ml-auto">
                                  {document.file_type}
                                </Badge>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  {selectedDocumentTitles.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedDocumentTitles.map((title) => (
                        <Badge key={title} variant="secondary" className="text-xs">
                          {title}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <FormDescription>
                    <Trans 
                      i18nKey="personas:referenceDocumentsHelp" 
                      defaults="Select the relevant product documentation to use as reference material to generate this ICP"
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isGenerating}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </Button>
              <Button type="submit" disabled={isGenerating}>
                {isGenerating ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    <Trans i18nKey="personas:generating" defaults="Generating..." />
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    <Trans i18nKey="personas:generateICP" defaults="Generate ICP" />
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 