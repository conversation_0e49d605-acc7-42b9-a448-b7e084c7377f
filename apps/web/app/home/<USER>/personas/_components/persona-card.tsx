'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { But<PERSON> } from '@kit/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@kit/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { MoreHorizontal, Trash2, Loader2, Pencil } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { PersonaData } from '~/types/persona';
import { useZero } from '~/hooks/use-zero';
import EditPersonaDialog from './edit-persona-dialog';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';


// Define a mapping for status to Tailwind CSS classes
const statusClasses: Record<string, string> = {
  Active: 'bg-green-200 text-green-800',
  Inactive: 'bg-gray-200 text-gray-800',
  Archived: 'bg-red-200 text-red-800',
};

export default function PersonaCard({ 
  id, 
  persona, 
  accountSlug, 
  isGenerating, 
  errorGenerating 
}: { 
  id: string;
  persona: PersonaData; 
  accountSlug: string; 
  isGenerating: boolean | null; 
  errorGenerating: boolean | null;
}) {
  const router = useRouter();
  const workspace = useTeamAccountWorkspace();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const zero = useZero();
  const { id: icpId } = useParams();
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      zero.mutate.personas.delete({
        id
      });
      router.refresh();
    } catch (error) {
      console.error('Failed to delete persona:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleRetry = async () => {
    await handleDelete();
    zero.mutate.personas.insert({
      id: crypto.randomUUID(),
      company_id: workspace.account.id,
      icp_id: icpId as string,
      withAi: true,
      data: {},
      name: 'null',
      error_generating: false,
    });
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase();
  };

  // Handle generating state
  if (isGenerating) {
    return (
      <Card className="max-w-md w-full h-64 flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-sm text-muted-foreground">
            Persona is being Generated, hold tight
          </p>
        </div>
      </Card>
    );
  }

  // Handle error state
  if (errorGenerating) {
    return (
      <Card className="max-w-md w-full h-64 flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            Sorry, there was a problem generating this persona.
          </p>
          <Button 
            variant="destructive" 
            size="sm"
            onClick={() => {
              // Retry logic can be implemented here
              handleRetry();
            }}
          >
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="max-w-md w-full min-h-64 relative overflow-hidden">
      {/* Dropdown Menu positioned absolutely, outside the Link */}
      <div className="absolute top-4 right-4 z-10">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              className="flex items-center gap-2"
              onClick={() => setIsEditDialogOpen(true)}
            >
              <Pencil className="h-4 w-4" /> Edit
            </DropdownMenuItem>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  className="flex items-center gap-2 text-destructive focus:text-destructive"
                  onSelect={(e) => e.preventDefault()}
                >
                  <Trash2 className="h-4 w-4" /> Delete
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Persona</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete {persona.name}? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>


        <CardHeader className="flex flex-row items-center gap-3 pb-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src={persona.avatar_url || undefined} alt={persona.name} />
            <AvatarFallback>{getInitials(persona.name)}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col flex-1 min-w-0">
            <CardTitle className="text-lg leading-tight truncate">{persona.name}</CardTitle>
            <p className="text-sm text-muted-foreground truncate">{persona.role}</p>
            {persona.location && (
              <p className="text-xs text-muted-foreground">{persona.location}</p>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="pt-0 space-y-3">
          {/* Status and Key Info */}
          <div className="flex flex-wrap items-center gap-2">
            <Badge variant="outline" className={statusClasses[persona.status || "Active"]}>
              {persona.status || "Active"}
            </Badge>
            {persona.department && (
              <Badge variant="secondary">{persona.department}</Badge>
            )}
            {persona.management_level && (
              <Badge variant="outline">{persona.management_level}</Badge>
            )}
          </div>

          {/* Company & Business Info - Only show if we have any of these fields */}
          {(persona.company_size || persona.buying_stage || persona.decision_authority) && (
            <div className="space-y-2 text-sm">
              {persona.company_size && (
                <div className="flex items-center">
                  <span className="font-medium mr-2">Company:</span>
                  <span className="text-muted-foreground">{persona.company_size}</span>
                </div>
              )}
              
              {persona.buying_stage && (
                <div className="flex items-center">
                  <span className="font-medium mr-2">Buying Stage:</span>
                  <span className="text-muted-foreground">{persona.buying_stage}</span>
                </div>
              )}

              {persona.decision_authority && (
                <div className="flex items-center">
                  <span className="font-medium mr-2">Authority:</span>
                  <span className="text-muted-foreground truncate">{persona.decision_authority}</span>
                </div>
              )}
            </div>
          )}
          
          {/* Industries - Only show if array has items */}
          {Array.isArray(persona.industries) && persona.industries.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Industries</p>
              <div className="flex flex-wrap gap-1">
                {persona.industries.map((industry, idx) => (
                  <Badge key={idx} variant="outline" className="bg-blue-50 text-blue-700 text-xs">{industry}</Badge>
                ))}
                {/* {persona.industries.length > 2 && (
                  <Badge variant="outline" className="text-xs">+{persona.industries.length - 2}</Badge>
                )} */}
              </div>
            </div>
          )}

          {/* Goals - Only show if array has items */}
          {Array.isArray(persona.goals) && persona.goals.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Key Goals</p>
              <p className="text-xs text-muted-foreground">
                {persona.goals.slice(0, 2).join(', ')}
                {persona.goals.length > 2 && '...'}
              </p>
            </div>
          )}

          {/* Topics - Only show if array has items */}
          {Array.isArray(persona.topics) && persona.topics.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Topics of Interest</p>
              <div className="flex flex-wrap gap-1">
                {persona.topics.map((topic, idx) => (
                  <Badge key={idx} variant="outline" className="bg-green-50 text-green-700 text-xs">{topic}</Badge>
                ))}
                {/* {persona.topics.length > 3 && (
                  <Badge variant="outline" className="text-xs">+{persona.topics.length - 3}</Badge>
                )} */}
              </div>
            </div>
          )}
            
          {/* Challenges - Only show if array has items */}
          {Array.isArray(persona.challenges) && persona.challenges.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Key Challenges</p>
              <p className="text-xs text-muted-foreground">
                {persona.challenges.join(', ')}
                {/* {persona.challenges.length > 2 && '...'} */}
              </p>
            </div>
          )}

          {/* Tech Stack - Only show if array has items */}
          {Array.isArray(persona.tech_stack) && persona.tech_stack.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Tech Stack</p>
              <div className="flex flex-wrap gap-1">
                {persona.tech_stack.map((tech, idx) => (
                  <Badge key={idx} variant="outline" className="bg-orange-50 text-orange-700 text-xs">{tech}</Badge>
                ))}
                {/* {persona.tech_stack.length > 3 && (
                  <Badge variant="outline" className="text-xs">+{persona.tech_stack.length - 3}</Badge>
                )} */}
              </div>
            </div>
          )}

          {/* Content Preferences - Only show if array has items */}
          {Array.isArray(persona.content_formats) && persona.content_formats.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Content Preferences</p>
              <div className="flex flex-wrap gap-1">
                {persona.content_formats.map((format, idx) => (
                  <Badge key={idx} variant="outline" className="bg-purple-50 text-purple-700 text-xs">{format}</Badge>
                ))}
                {/* {persona.content_formats.length > 2 && (
                  <Badge variant="outline" className="text-xs">+{persona.content_formats.length - 2}</Badge>
                )} */}
              </div>
            </div>
          )}

          {/* Budget Range - Only show if it exists */}
          {persona.budget_range && (
            <div className="text-sm">
              <span className="font-medium mr-2">Budget:</span>
              <span className="text-muted-foreground">{persona.budget_range}</span>
            </div>
          )}

          {/* Info Preferences - Only show if array has items */}
          {Array.isArray(persona.info_preferences) && persona.info_preferences.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Info Preferences</p>
              <p className="text-xs text-muted-foreground">
                {persona.info_preferences.slice(0, 2).join(', ')}
                {persona.info_preferences.length > 2 && '...'}
              </p>
            </div>
          )}
        </CardContent>


      {/* Edit Persona Dialog */}
      <EditPersonaDialog
        persona={{
          id,
          name: persona.name,
          created_at: null,
          updated_at: null,
          company_id: null,
          icp_id: null,
          data: persona,
          is_generating: isGenerating,
          error_generating: errorGenerating,
        }}
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
      />
    </Card>
  );
}