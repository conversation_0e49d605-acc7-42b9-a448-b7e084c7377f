'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@kit/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { updatePersona } from '~/services/persona';
import { Persona } from '~/types/persona';
import { MultiSelect } from './multi-select';
import Link from 'next/link';

const managementLevels = [
  { label: 'C-level', value: 'C-level' },
  { label: 'VP-level', value: 'VP-level' },
  { label: 'Director', value: 'Director' },
  { label: 'Manager', value: 'Manager' },
  { label: 'Individual Contributor', value: 'Individual Contributor' },
];

const companySize = [
  { label: '1-10 employees', value: '1-10' },
  { label: '11-50 employees', value: '11-50' },
  { label: '51-200 employees', value: '51-200' },
  { label: '201-500 employees', value: '201-500' },
  { label: '501-1000 employees', value: '501-1000' },
  { label: '1001-5000 employees', value: '1001-5000' },
  { label: '5001+ employees', value: '5001+' },
];

const decisionMakingAuthority = [
  { label: 'Final decision maker', value: 'Final decision maker' },
  { label: 'Significant influence', value: 'Significant influence' },
  { label: 'Some influence', value: 'Some influence' },
  { label: 'Researcher only', value: 'Researcher only' },
];

const buyingStage = [
  { label: 'Awareness', value: 'Awareness' },
  { label: 'Consideration', value: 'Consideration' },
  { label: 'Decision', value: 'Decision' },
  { label: 'Implementation', value: 'Implementation' },
];

const communicationStyle = [
  { label: 'Technical', value: 'Technical' },
  { label: 'Business-focused', value: 'Business-focused' },
  { label: 'Conversational', value: 'Conversational' },
  { label: 'Formal', value: 'Formal' },
  { label: 'Visual/Graphics', value: 'Visual/Graphics' },
];

const contentLength = [
  { label: 'Short-form', value: 'Short-form' },
  { label: 'Medium', value: 'Medium' },
  { label: 'Long-form/Detailed', value: 'Long-form/Detailed' },
];

const EditPersonaSchema = z.object({
  name: z.string().min(1, 'Persona name is required'),
  role: z.string().min(1, 'Job title/role is required'),
  department: z.string().optional(),
  management_level: z.string().optional(),
  status: z.enum(['Active', 'Inactive', 'Archived']).default('Active'),
  
  // Company details
  industries: z.array(z.string()).min(0).default([]),
  company_size: z.string().optional(),
  location: z.string().optional(),
  tech_stack: z.array(z.string()).min(0).default([]),
  budget_range: z.string().optional(),
  
  // Behavioral profile
  challenges: z.array(z.string()).min(0).default([]),
  goals: z.array(z.string()).min(0).default([]),
  decision_authority: z.string().optional(),
  buying_stage: z.string().optional(),
  info_preferences: z.array(z.string()).min(0).default([]),
  
  // Content preferences
  content_formats: z.array(z.string()).min(0).default([]),
  communication_style: z.string().optional(),
  content_length: z.string().optional(),
  channels: z.array(z.string()).min(0).default([]),
  topics: z.array(z.string()).min(0).default([]),
});

type EditPersonaFormData = z.infer<typeof EditPersonaSchema>;

interface EditPersonaFormProps {
  persona: Persona;
  accountSlug: string;
}

export default function EditPersonaForm({ persona, accountSlug }: EditPersonaFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Convert JSON fields to arrays if they exist
  const defaultValues: EditPersonaFormData = {
    name: persona.name,
    role: persona.role,
    department: persona.department || '',
    management_level: persona.management_level || '',
    status: persona.status as 'Active' | 'Inactive' | 'Archived',
    industries: Array.isArray(persona.industries) ? persona.industries as string[] : [],
    company_size: persona.company_size || '',
    location: persona.location || '',
    tech_stack: Array.isArray(persona.tech_stack) ? persona.tech_stack as string[] : [],
    budget_range: persona.budget_range || '',
    challenges: Array.isArray(persona.challenges) ? persona.challenges as string[] : [],
    goals: Array.isArray(persona.goals) ? persona.goals as string[] : [],
    decision_authority: persona.decision_authority || '',
    buying_stage: persona.buying_stage || '',
    info_preferences: Array.isArray(persona.info_preferences) ? persona.info_preferences as string[] : [],
    content_formats: Array.isArray(persona.content_formats) ? persona.content_formats as string[] : [],
    communication_style: persona.communication_style || '',
    content_length: persona.content_length || '',
    channels: Array.isArray(persona.channels) ? persona.channels as string[] : [],
    topics: Array.isArray(persona.topics) ? persona.topics as string[] : [],
  };

  const form = useForm<EditPersonaFormData>({
    resolver: zodResolver(EditPersonaSchema),
    defaultValues,
  });

  const onSubmit = async (data: EditPersonaFormData) => {
    try {
      setIsSubmitting(true);
      await updatePersona({
        ...data,
        id: persona.id,
      });
      
      router.push(`/home/<USER>/personas/${persona.id}`);
      router.refresh();
    } catch (error) {
      console.error('Failed to update persona:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <Button variant="ghost" asChild className="gap-2">
          <Link href={`/home/<USER>/personas/${persona.id}`}>
            <ArrowLeft className="h-4 w-4" /> Back to Persona
          </Link>
        </Button>
        
        <Button 
          onClick={form.handleSubmit(onSubmit)}
          disabled={isSubmitting}
          className="gap-2"
        >
          <Save className="h-4 w-4" />
          {isSubmitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
      
      <Form {...form}>
        <form className="space-y-6">
          <Tabs defaultValue="basic">
            <TabsList className="grid grid-cols-4 w-full mb-6">
              <TabsTrigger value="basic">Basic Information</TabsTrigger>
              <TabsTrigger value="company">Company Details</TabsTrigger>
              <TabsTrigger value="behavior">Behavioral Profile</TabsTrigger>
              <TabsTrigger value="content">Content Preferences</TabsTrigger>
            </TabsList>
            
            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Persona Name*</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormDescription>
                          Give this persona a memorable name that represents their role
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Job Title/Role*</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., IT, Marketing" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="management_level"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Management Level</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select management level" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {managementLevels.map((level) => (
                              <SelectItem key={level.value} value={level.value}>
                                {level.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Active">Active</SelectItem>
                            <SelectItem value="Inactive">Inactive</SelectItem>
                            <SelectItem value="Archived">Archived</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>
            
            {/* Company Details Tab */}
            <TabsContent value="company" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="industries"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Industries</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add industry sectors"
                            selected={field.value}
                            options={[
                              'Technology', 'Finance', 'Healthcare', 'Education', 
                              'Manufacturing', 'Retail', 'Media', 'Government'
                            ].map(i => ({ label: i, value: i }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="company_size"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company Size</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select company size" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {companySize.map((size) => (
                              <SelectItem key={size.value} value={size.value}>
                                {size.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., North America, Global" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="tech_stack"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Technology Stack</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add technologies"
                            selected={field.value}
                            options={[
                              'AWS', 'Azure', 'Google Cloud', 'React', 'Node.js',
                              'Python', 'Java', 'Kubernetes', 'Docker'
                            ].map(t => ({ label: t, value: t }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="budget_range"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget Range</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., $10k-$50k annually" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>
            
            {/* Behavioral Profile Tab */}
            <TabsContent value="behavior" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="challenges"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Challenges</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add key challenges"
                            selected={field.value}
                            options={[
                              'Cost management', 'Scalability', 'Integration', 
                              'Security', 'Compliance', 'User adoption', 'Technical debt'
                            ].map(c => ({ label: c, value: c }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="goals"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Goals and Objectives</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add key goals"
                            selected={field.value}
                            options={[
                              'Reduce costs', 'Increase efficiency', 'Improve security',
                              'Digital transformation', 'Revenue growth', 'Innovation'
                            ].map(g => ({ label: g, value: g }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="decision_authority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Decision-Making Authority</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select decision-making role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {decisionMakingAuthority.map((auth) => (
                              <SelectItem key={auth.value} value={auth.value}>
                                {auth.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="buying_stage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Typical Buying Stage</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select buying stage" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {buyingStage.map((stage) => (
                              <SelectItem key={stage.value} value={stage.value}>
                                {stage.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="info_preferences"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Information Preferences</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add information preferences"
                            selected={field.value}
                            options={[
                              'Technical details', 'Case studies', 'ROI analysis',
                              'How-to guides', 'Industry research', 'Peer reviews', 'Competitive comparisons'
                            ].map(p => ({ label: p, value: p }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>
            
            {/* Content Preferences Tab */}
            <TabsContent value="content" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="content_formats"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preferred Content Formats</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add preferred formats"
                            selected={field.value}
                            options={[
                              'Blog posts', 'White papers', 'Webinars', 'Videos',
                              'Infographics', 'Case studies', 'Podcasts', 'Demos'
                            ].map(f => ({ label: f, value: f }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="communication_style"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Communication Style</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select communication style" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {communicationStyle.map((style) => (
                              <SelectItem key={style.value} value={style.value}>
                                {style.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="content_length"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Content Length Preference</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select content length" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {contentLength.map((length) => (
                              <SelectItem key={length.value} value={length.value}>
                                {length.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="channels"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preferred Channels</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add preferred channels"
                            selected={field.value}
                            options={[
                              'Email', 'LinkedIn', 'Twitter', 'Industry events',
                              'Webinars', 'Direct mail', 'Website', 'Search'
                            ].map(c => ({ label: c, value: c }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="topics"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Topics of Interest</FormLabel>
                        <FormControl>
                          <MultiSelect
                            placeholder="Select or add topics of interest"
                            selected={field.value}
                            options={[
                              'Digital transformation', 'Cloud migration', 'Security', 
                              'AI/ML', 'Automation', 'Cost optimization', 'Compliance'
                            ].map(t => ({ label: t, value: t }))}
                            onChange={field.onChange}
                            creatable
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-end mt-6">
            <Button 
              type="button"
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}