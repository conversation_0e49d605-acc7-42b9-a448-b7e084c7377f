'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@kit/ui/alert-dialog';
import { ArrowLeft, Pencil, Trash2, Building, Target, MessageSquare, BarChart4 } from 'lucide-react';

import { formatDistanceToNow } from 'date-fns';
import { deletePersona } from '~/services/persona';
import { Persona } from '~/types/persona';

// Define a mapping for status to Tailwind CSS classes
const statusClasses: Record<string, string> = {
  Active: 'bg-green-200 text-green-800',
  Inactive: 'bg-gray-200 text-gray-800',
  Archived: 'bg-red-200 text-red-800',
};

export default function PersonaDetail({ 
  persona, 
  accountSlug 
}: { 
  persona: Persona, 
  accountSlug: string 
}) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deletePersona(persona.id);
      router.push(`/home/<USER>/personas`);
    } catch (error) {
      console.error('Failed to delete persona:', error);
      setIsDeleting(false);
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase();
  };

  // Format the dates
  const createdDate = formatDistanceToNow(new Date(persona.created_at), { addSuffix: true });
  const updatedDate = formatDistanceToNow(new Date(persona.updated_at), { addSuffix: true });
  
  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="gap-2">
          <ArrowLeft className="h-4 w-4" /> Back to Personas
        </Button>
        
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/home/<USER>/personas/${persona.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" /> Edit Persona
            </Link>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Persona</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete {persona.name}? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground"
                >
                  {isDeleting ? "Deleting..." : "Delete"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column for profile summary */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={persona.avatar_url || undefined} alt={persona.name} />
                <AvatarFallback>{getInitials(persona.name)}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-2xl">{persona.name}</CardTitle>
                <CardDescription className="text-base">{persona.role}</CardDescription>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className={statusClasses[persona.status || "Active"]}>
                  {persona.status}
                </Badge>
                {persona.department && (
                  <Badge variant="secondary">{persona.department}</Badge>
                )}
                {persona.management_level && (
                  <Badge variant="outline">{persona.management_level}</Badge>
                )}
              </div>
              
              {persona.company_size && (
                <div className="flex items-center text-sm">
                  <span className="font-medium mr-2">Company Size:</span>
                  <span>{persona.company_size}</span>
                </div>
              )}
              
              {persona.location && (
                <div className="flex items-center text-sm">
                  <span className="font-medium mr-2">Location:</span>
                  <span>{persona.location}</span>
                </div>
              )}
              
              {persona.budget_range && (
                <div className="flex items-center text-sm">
                  <span className="font-medium mr-2">Budget Range:</span>
                  <span>{persona.budget_range}</span>
                </div>
              )}
              
              <div className="pt-2 border-t border-border">
                <p className="text-xs text-muted-foreground">Created {createdDate}</p>
                <p className="text-xs text-muted-foreground">Updated {updatedDate}</p>
              </div>
            </CardContent>
          </Card>
          
          {/* Industries section */}
          {Array.isArray(persona.industries) && persona.industries.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Building className="h-4 w-4" /> Industries
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {(persona.industries as string[]).map((industry, idx) => (
                    <Badge key={idx} variant="outline" className="bg-primary/10">{industry}</Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Tech Stack section */}
          {Array.isArray(persona.tech_stack) && persona.tech_stack.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Tech Stack</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {(persona.tech_stack as string[]).map((tech, idx) => (
                    <Badge key={idx} variant="outline" className="bg-secondary/20">{tech}</Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        
        {/* Right column for tabs */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="profile">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <Target className="h-4 w-4" /> Profile
              </TabsTrigger>
              <TabsTrigger value="content" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" /> Content
              </TabsTrigger>
              <TabsTrigger value="campaigns" className="flex items-center gap-2">
                <BarChart4 className="h-4 w-4" /> Campaigns
              </TabsTrigger>
            </TabsList>
            
            {/* Profile tab */}
            <TabsContent value="profile" className="space-y-4">
              {/* Challenges and Goals */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Challenges */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Challenges</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {Array.isArray(persona.challenges) && persona.challenges.length > 0 ? (
                      <ul className="list-disc list-inside space-y-1">
                        {(persona.challenges as string[]).map((challenge, idx) => (
                          <li key={idx}>{challenge}</li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-muted-foreground">No challenges defined</p>
                    )}
                  </CardContent>
                </Card>
                
                {/* Goals */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Goals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {Array.isArray(persona.goals) && persona.goals.length > 0 ? (
                      <ul className="list-disc list-inside space-y-1">
                        {(persona.goals as string[]).map((goal, idx) => (
                          <li key={idx}>{goal}</li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-muted-foreground">No goals defined</p>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              {/* Decision Authority and Buying Stage */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Decision Authority */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Decision Authority</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {persona.decision_authority ? (
                      <Badge variant="secondary">{persona.decision_authority}</Badge>
                    ) : (
                      <p className="text-muted-foreground">Not specified</p>
                    )}
                  </CardContent>
                </Card>
                
                {/* Buying Stage */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Buying Stage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {persona.buying_stage ? (
                      <Badge variant="secondary">{persona.buying_stage}</Badge>
                    ) : (
                      <p className="text-muted-foreground">Not specified</p>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              {/* Information Preferences */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Information Preferences</CardTitle>
                </CardHeader>
                <CardContent>
                  {Array.isArray(persona.info_preferences) && persona.info_preferences.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {(persona.info_preferences as string[]).map((pref, idx) => (
                        <Badge key={idx} variant="outline">{pref}</Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No information preferences defined</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Content tab */}
            <TabsContent value="content" className="space-y-4">
              {/* Content Formats */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Preferred Content Formats</CardTitle>
                </CardHeader>
                <CardContent>
                  {Array.isArray(persona.content_formats) && persona.content_formats.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {(persona.content_formats as string[]).map((format, idx) => (
                        <Badge key={idx} variant="outline">{format}</Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No preferred content formats defined</p>
                  )}
                </CardContent>
              </Card>
              
              {/* Communication Style and Content Length */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Communication Style */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Communication Style</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {persona.communication_style ? (
                      <Badge variant="secondary">{persona.communication_style}</Badge>
                    ) : (
                      <p className="text-muted-foreground">Not specified</p>
                    )}
                  </CardContent>
                </Card>
                
                {/* Content Length */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Content Length</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {persona.content_length ? (
                      <Badge variant="secondary">{persona.content_length}</Badge>
                    ) : (
                      <p className="text-muted-foreground">Not specified</p>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              {/* Channels */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Preferred Channels</CardTitle>
                </CardHeader>
                <CardContent>
                  {Array.isArray(persona.channels) && persona.channels.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {(persona.channels as string[]).map((channel, idx) => (
                        <Badge key={idx} variant="outline">{channel}</Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No preferred channels defined</p>
                  )}
                </CardContent>
              </Card>
              
              {/* Topics */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Topics of Interest</CardTitle>
                </CardHeader>
                <CardContent>
                  {Array.isArray(persona.topics) && persona.topics.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {(persona.topics as string[]).map((topic, idx) => (
                        <Badge key={idx} variant="outline">{topic}</Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No topics of interest defined</p>
                  )}
                </CardContent>
              </Card>
              
              {/* Associated Content */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Associated Content</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {persona.content_count ? `${persona.content_count} content items` : "No content associated yet"}
                  </p>
                  
                  {!persona.content_count && (
                    <div className="mt-4">
                      <Button variant="outline" className="w-full" asChild>
                        <Link href={`/home/<USER>/studio?personaId=${persona.id}`}>
                          Create Content for this Persona
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Campaigns tab */}
            <TabsContent value="campaigns" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Associated Campaigns</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    No campaigns associated with this persona yet.
                  </p>
                  <div className="mt-4">
                    <Button variant="outline" className="w-full" asChild>
                      <Link href={`/home/<USER>/campaigns?personaId=${persona.id}`}>
                        Add to Campaign
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}