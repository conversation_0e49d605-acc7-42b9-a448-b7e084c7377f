'use client';

import { Button } from '@kit/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { UserRound, Plus, ChevronDown, Sparkles, Edit } from 'lucide-react';
import { Trans } from '@kit/ui/trans';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface EmptyPersonasProps {
  setCreatePersonaOpen: (open: boolean) => void;
  icpId: string;
}

export default function EmptyPersonas({ setCreatePersonaOpen,  icpId }: EmptyPersonasProps) {
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const handleAIGeneration = () => {
    console.log('Generate persona with AI');
    zero.mutate.personas.insert({
      id: crypto.randomUUID(),
      company_id: workspace.account.id,
      icp_id: icpId,
      withAi: true,
      data: {},
      name: 'null',
      error_generating: false,
    });
  };

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 border border-dashed rounded-lg">
      <div className="bg-primary/10 p-4 rounded-full mb-4">
        <UserRound className="h-12 w-12 text-primary" />
      </div>
      <h3 className="text-xl font-semibold mb-2">
        No personas in this ICP yet
      </h3>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create First Persona
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="center">
          <DropdownMenuItem className='cursor-pointer' onClick={handleAIGeneration}>
            <Sparkles className="h-4 w-4 mr-2" />
            <Trans i18nKey="personas:generateWithAI" defaults="Generate With AI" />
          </DropdownMenuItem>
          <DropdownMenuItem className='cursor-pointer' onClick={() => setCreatePersonaOpen(true)}>
            <Edit className="h-4 w-4 mr-2" />
            <Trans i18nKey="personas:createManually" defaults="Create Manually" />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}