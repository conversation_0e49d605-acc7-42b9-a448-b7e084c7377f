'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@kit/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { MoreHorizontal, Trash2, Pencil, Eye, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { PersonaData } from '~/types/persona';
import { useZero } from '~/hooks/use-zero';
import EditPersonaDialog from './edit-persona-dialog';

// Define a mapping for status to Tailwind CSS classes
const statusClasses: Record<string, string> = {
  Active: 'bg-green-200 text-green-800',
  Inactive: 'bg-gray-200 text-gray-800',
  Archived: 'bg-red-200 text-red-800',
};

export default function PersonaListItem({ 
  id, 
  persona, 
  accountSlug, 
  isGenerating, 
  errorGenerating 
}: { 
  id: string;
  persona: PersonaData;
  accountSlug: string;
  isGenerating?: boolean | null;
  errorGenerating?: boolean | null;
}) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const zero = useZero();

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      zero.mutate.personas.delete({
        id
      });
      router.refresh();
    } catch (error) {
      console.error('Failed to delete persona:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase();
  };

  // Handle generating state
  if (isGenerating) {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-center py-8">
          <div className="text-center space-y-4">
            <Loader2 className="h-6 w-6 animate-spin mx-auto text-primary" />
            <p className="text-sm text-muted-foreground">
              Persona is being Generated, hold tight
            </p>
          </div>
        </div>
      </Card>
    );
  }

  // Handle error state
  if (errorGenerating) {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-center py-8">
          <div className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              Sorry, there was a problem generating this persona.
            </p>
            <Button 
              variant="destructive" 
              size="sm"
              onClick={() => {
                // Retry logic can be implemented here
                console.log('Retry clicked');
              }}
            >
              Try Again
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Avatar className="h-10 w-10">
            <AvatarImage src={persona?.avatar_url || undefined} alt={persona?.name || ''} />
            <AvatarFallback>{getInitials(persona?.name || 'Unknown')}</AvatarFallback>
          </Avatar>
          
          <div className="space-y-1">
            <div className="flex items-center">
              <Link href={`/home/<USER>/personas/${id}`} className="font-medium hover:underline">
                {persona?.name || 'Unknown'}
              </Link>
              <span className="mx-2 text-muted-foreground">•</span>
              <p className="text-sm text-muted-foreground">{persona?.role || ''}</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-2">
              <Badge variant="outline" className={statusClasses[persona?.status || "Active"]}>
                {persona?.status || 'Active'}
              </Badge>
              {persona?.department && (
                <Badge variant="secondary">{persona.department}</Badge>
              )}
              {persona?.management_level && (
                <Badge variant="outline">{persona.management_level}</Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link 
                  href={`/home/<USER>/personas/${id}`} 
                  className="flex items-center gap-2"
                >
                  <Eye className="h-4 w-4" /> View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => setIsEditDialogOpen(true)}
              >
                <Pencil className="h-4 w-4" /> Edit
              </DropdownMenuItem>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem
                    className="flex items-center gap-2 text-destructive focus:text-destructive"
                    onSelect={(e) => e.preventDefault()}
                  >
                    <Trash2 className="h-4 w-4" /> Delete
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Persona</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete {persona?.name || 'this persona'}? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      disabled={isDeleting}
                    >
                      {isDeleting ? "Deleting..." : "Delete"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Edit Persona Dialog */}
      <EditPersonaDialog
        persona={{
          id,
          name: persona?.name || '',
          created_at: null,
          updated_at: null,
          company_id: null,
          icp_id: null,
          data: persona,
          is_generating: isGenerating || null,
          error_generating: errorGenerating || null,
        }}
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
      />
    </Card>
  );
}