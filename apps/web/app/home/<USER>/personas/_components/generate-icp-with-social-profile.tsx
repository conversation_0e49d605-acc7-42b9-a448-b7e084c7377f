'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
// TO DO: remove deprecated lucide icons for linkedin
import { Linkedin, Plus, X } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';

interface GenerateICPWithSocialProfileDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const LinkedInUrlSchema = z.string().url('Please enter a valid URL').refine(
  (url) => url.includes('linkedin.com'),
  { message: 'Please enter a valid LinkedIn URL' }
);

const GenerateICPWithSocialProfileSchema = z.object({
  linkedinUrls: z.array(
    z.object({
      url: LinkedInUrlSchema,
    })
  ).min(1, 'Please enter at least one LinkedIn URL').max(5, 'Maximum 5 LinkedIn URLs allowed'),
});

type GenerateICPWithSocialProfileFormData = z.infer<typeof GenerateICPWithSocialProfileSchema>;

export function GenerateICPWithSocialProfileDialog({
  isOpen,
  onClose
}: GenerateICPWithSocialProfileDialogProps) {
  const form = useForm<GenerateICPWithSocialProfileFormData>({
    resolver: zodResolver(GenerateICPWithSocialProfileSchema),
    defaultValues: {
      linkedinUrls: [{ url: '' }],
    },
  });
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'linkedinUrls',
  });

  const handleSubmit = (data: GenerateICPWithSocialProfileFormData) => {
    const urls = data.linkedinUrls.map(item => item.url);
    // TODO: Implement actual LinkedIn profile processing
    zero.mutate.icps.insert({
        id: crypto.randomUUID(),
        values : {
            company_id: account.id,
            withAi: true,
            withLinkedIn: true,
            name: null,
            data: {},
            is_generating: true,
            error_generating: false,
            reference_material: [],
            reference_description: '',
            linkedInUrls: urls
        }
    });
    form.reset();
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  const addUrlField = () => {
    if (fields.length < 5) {
      append({ url: '' });
    }
  };

  const removeUrlField = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const canAddMore = fields.length < 5;
  const canRemove = fields.length > 1;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Linkedin className="h-5 w-5" />
            <Trans i18nKey="personas:generateICPWithLinkedIn" defaults="Generate ICP with LinkedIn Profiles" />
          </DialogTitle>
          <DialogDescription>
            <Trans 
              i18nKey="personas:generateICPWithLinkedInDescription" 
              defaults="Enter LinkedIn profile URLs of your ideal customers to generate an ICP based on their professional information and company details."
            />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <FormLabel className="text-sm font-medium">
                  <Trans i18nKey="personas:linkedinUrls" defaults="LinkedIn Profile URLs" />
                </FormLabel>
                {canAddMore && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addUrlField}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    <Trans i18nKey="personas:addUrl" defaults="Add" />
                  </Button>
                )}
              </div>

              {fields.map((field, index) => (
                <FormField
                  key={field.id}
                  control={form.control}
                  name={`linkedinUrls.${index}.url`}
                  render={({ field: fieldProps }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormControl>
                          <Input
                            placeholder="https://linkedin.com/in/profile-name"
                            {...fieldProps}
                          />
                        </FormControl>
                        {canRemove && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeUrlField(index)}
                            className="px-2"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}

              <FormDescription>
                <Trans 
                  i18nKey="personas:linkedinUrlsHelp" 
                  defaults="Enter LinkedIn profile URLs of your ideal customers. You can add up to 5 profiles to help us generate a comprehensive ICP."
                />
              </FormDescription>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </Button>
              <Button
                type="submit"
                className="flex items-center gap-2"
              >
                <Linkedin className="h-4 w-4" />
                  <Trans i18nKey="personas:generateICP" defaults="Generate ICP" />
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
