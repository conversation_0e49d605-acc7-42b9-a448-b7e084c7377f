import { getSupabaseServerClient } from "@kit/supabase/server-client";
import { createTeamAccountsApi } from "@kit/team-accounts/api";
import { notFound } from "next/navigation";
import { getICPById } from "~/services/icp";
interface EditPersonaPageProps {
  params: Promise<{ account: string; id: string }>;
}

export default async function EditPersonaPage(props: EditPersonaPageProps) {
  const api = createTeamAccountsApi(getSupabaseServerClient());
  console.log('api', api);
  const params = await props.params;
  console.log('params', params);
  const accountSlug = params.account;
  console.log('accountSlug', accountSlug);
  const personaId = params.id;
  console.log('personaId', personaId);
  const data = await api.getTeamAccount(accountSlug);
  console.log('data', data);
  const account = {
    id: data.id,
    name: data.name,
    pictureUrl: data.picture_url,
    slug: data.slug as string,
    primaryOwnerUserId: data.primary_owner_user_id,
  };

  const icp = await getICPById(personaId);
  console.log('persdfdsfsona', icp);
  if (!icp || icp.company_id !== account.id) {
    notFound();
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Edit Persona</h1>
      {/* <EditPersonaForm 
        persona={persona}
        accountSlug={accountSlug}
      /> */}
    </div>
  );
}