import { CampaignNav } from "./_components/campaignNav";
import { BottomNav } from "./_components/bottom-nav";
import { CampaignProvider } from "./_components/campaign-context";

type LayoutProps = {
  children: React.ReactNode;
  params: {
    slug: string; // The dynamic parameter you want to access
    account: string; // The dynamic parameter you want to access
  };
};

export default async function CampaignLayout({ children, params }: LayoutProps) {
  const campaignSlug = (await params).slug;
  const account = (await params).account;
  
  return (
    <CampaignProvider>
      <div className="flex flex-col h-full relative">
        <CampaignNav account={account} campaignSlug={campaignSlug} />
        <div className="flex-1 overflow-y-auto pb-24">
          {children}
        </div>
        <BottomNav 
          account={account} 
          campaignSlug={campaignSlug} 
        />
      </div>
    </CampaignProvider>
  )
}