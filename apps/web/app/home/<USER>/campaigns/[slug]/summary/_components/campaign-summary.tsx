'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { CheckCircle2, ArrowRight, FileText, Layers } from 'lucide-react';
import { Campaign } from '~/types/Campaign';
import { CampaignIdea } from '~/types/campaign-idea';
import { CompanyContent } from '~/types/company-content';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface CampaignSummaryProps {
  campaign: Campaign;
  campaignIdeas: CampaignIdea[];
  campaignTasks: CompanyContent[];
}

export function CampaignSummary({ campaign, campaignIdeas, campaignTasks }: CampaignSummaryProps) {
  const router = useRouter();
  const workspace = useTeamAccountWorkspace();
  const [contentTypeCounts, setContentTypeCounts] = useState<Record<string, number>>({});
  
  // Group tasks by content type with debug
  useEffect(() => {
    const counts: Record<string, number> = {};
    console.log("Campaign Tasks:", campaignTasks);
    
    // Log each task type for debugging
    campaignTasks.forEach(task => {
      console.log(`Task: ${task.task_title}, Type: ${task.content_type}, Status: ${task.status}, Draft: ${task.is_draft}`);
      
      if (task.content_type) {
        counts[task.content_type] = (counts[task.content_type] || 0) + 1;
      }
    });
    
    console.log("Content Type Counts:", counts);
    setContentTypeCounts(counts);
  }, [campaignTasks]);
  
  // Only show ideas that have tasks
  const ideasWithTasks = campaignIdeas.filter(idea => {
    const hasTasks = campaignTasks.some(task => task.idea_id === idea.id);
    console.log(`Idea ${idea.id}, has tasks: ${hasTasks}`);
    return hasTasks;
  });
  
  console.log('Campaign Ideas:', campaignIdeas.length);
  console.log('Ideas with tasks:', ideasWithTasks.length);
  
  // Get count of tasks by status
  const taskStatusCounts = campaignTasks.reduce((acc, task) => {
    const status = task.status || 'to do';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const navigateToContentStudio = () => {
    if (workspace.account?.slug) {
      router.push(`/home/<USER>/tasks?campaign_id=${campaign.id}`);
    }
  };

  return (
    <div className="space-y-8">
      <section>
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Campaign Summary</h1>
          <p className="text-muted-foreground mt-2">
            Your campaign is ready for content creation. Here&apos;s a summary of your campaign tasks.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <StatsCard 
            title="Campaign Ideas" 
            value={ideasWithTasks.length} 
            description={`${campaignIdeas.length} total, ${ideasWithTasks.length} with tasks`} 
            icon={<FileText className="h-4 w-4" />} 
          />
          <StatsCard 
            title="Content Tasks" 
            value={campaignTasks.length} 
            description={`${taskStatusCounts['completed'] || 0} completed, ${campaignTasks.length - (taskStatusCounts['completed'] || 0)} pending`}
            icon={<CheckCircle2 className="h-4 w-4" />} 
          />
          <StatsCard 
            title="Content Types" 
            value={Object.keys(contentTypeCounts).length} 
            description={Object.keys(contentTypeCounts).length > 0 
              ? `${Object.keys(contentTypeCounts).join(', ')}` 
              : 'No content types found'} 
            icon={<Layers className="h-4 w-4" />} 
          />
        </div>
        
        <Card className="mb-8">
          <CardHeader className="pb-3">
            <CardTitle>Content Distribution</CardTitle>
            <CardDescription>
              Breakdown of content tasks by type and status
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-2">By Content Type</h3>
              <div className="flex flex-wrap gap-3">
                {Object.entries(contentTypeCounts).map(([type, count]) => (
                  <Badge key={type} variant="secondary" className="text-sm py-1 px-3">
                    {type}: {count}
                  </Badge>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium mb-2">By Status</h3>
              <div className="flex flex-wrap gap-3">
                {Object.entries(taskStatusCounts).map(([status, count]) => (
                  <Badge key={status} variant="outline" className="text-sm py-1 px-3">
                    {status}: {count}
                  </Badge>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium mb-2">By Channel</h3>
              <div className="flex flex-wrap gap-3">
                {Object.entries(campaignTasks.reduce((acc, task) => {
                  if (task.channel) {
                    acc[task.channel] = (acc[task.channel] || 0) + 1;
                  }
                  return acc;
                }, {} as Record<string, number>)).map(([channel, count]) => (
                  <Badge key={channel} variant="secondary" className="text-sm py-1 px-3">
                    {channel}: {count}
                  </Badge>
                ))}
                {Object.keys(campaignTasks.reduce((acc, task) => {
                  if (task.channel) {
                    acc[task.channel] = true;
                  }
                  return acc;
                }, {} as Record<string, boolean>)).length === 0 && (
                  <span className="text-sm text-muted-foreground">No channel data available</span>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={navigateToContentStudio} 
              className="w-full"
            >
              Go to Task Management
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </section>
    
    </div>
  );
}

function StatsCard({ title, value, description, icon }: { 
  title: string; 
  value: number; 
  description: string;
  icon: React.ReactNode;
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}
