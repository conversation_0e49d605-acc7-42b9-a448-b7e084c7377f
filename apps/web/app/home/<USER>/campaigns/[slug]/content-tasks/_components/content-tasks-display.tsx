'use client';

import { Badge } from "@kit/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@kit/ui/card";
import { cn } from "@kit/ui/utils";
import { ContentTasksDisplayProps } from "../_types/component-types";
import { Skeleton } from "@kit/ui/skeleton";
import { Spinner } from "@kit/ui/spinner";
import { X } from 'lucide-react';
import { Button } from "@kit/ui/button";
import { formatDate } from '~/lib/utils/date-formatter';

export default function ContentTasksDisplay({ 
  contentTasks, 
  contentTypes,
  activeContentType, 
  onContentTypeChange,
  generateContentTasks,
  isLoading,
  onDelete
}: ContentTasksDisplayProps) {
  // Filter content based on active content type
  const filteredContent = activeContentType === 'all'
    ? contentTasks
    : contentTasks.filter(content => content.content_type === activeContentType);
  console.log(filteredContent);
  // Early return for loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold tracking-tight">Content Tasks</h2>
          <Button onClick={() => generateContentTasks()}>Re-generate Content Tasks</Button>
        </div>
        <div className="relative">
          <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
            <Spinner className="w-8 h-8 mb-2" />
            <p className="text-muted-foreground">Tasks are being generated...</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 opacity-50">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-[200px] w-full" />
            ))}
          </div>
        </div>
      </div>
    );
  }
  console.log({filteredContent})
  // Early return if we don't have the necessary data
  if (!contentTasks || contentTasks.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold tracking-tight">Content Tasks</h2>
          <Button onClick={() => generateContentTasks()}>Re-generate Content Tasks</Button>
        </div>
        <div className="text-center py-8 text-muted-foreground">
          No content tasks generated yet.
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold tracking-tight">Content Tasks</h2>
        <Button onClick={() => generateContentTasks()}>Re-generate Content Tasks</Button>
      </div>
      
      <div className="border-b flex gap-2">
        <button
          onClick={() => onContentTypeChange('all')}
          className={cn(
            "px-4 py-2 relative",
            "hover:text-foreground/80 transition-colors",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            activeContentType === 'all' && "text-foreground",
            activeContentType !== 'all' && "text-muted-foreground"
          )}
        >
          All
          {activeContentType === 'all' && (
            <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-foreground" />
          )}
        </button>
        
        {contentTypes && contentTypes.map((type) => (
          <button
            key={type}
            onClick={() => onContentTypeChange(type)}
            className={cn(
              "px-4 py-2 relative",
              "hover:text-foreground/80 transition-colors",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
              activeContentType === type && "text-foreground",
              activeContentType !== type && "text-muted-foreground"
            )}
          >
            {type.charAt(0).toUpperCase() + type.slice(1)}
            {activeContentType === type && (
              <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-foreground" />
            )}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredContent.map((content) => (
          <Card key={content.id} className="hover:shadow-md transition-shadow relative">
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 h-6 w-6 hover:bg-destructive/10"
              onClick={() => onDelete?.(content.id)}
            >
              <X className="h-4 w-4 text-destructive" />
            </Button>
            <CardHeader>
              <div className="flex flex-col gap-2 items-center justify-between">
                <CardTitle className="text-lg font-semibold">
                  {content.task_title}
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-2 text-sm">
                {formatDate(content.scheduled_publishing_time)}
              </p>
              <p className="text-muted-foreground text-sm">
                {content.task_description}
              </p>
              <Badge className="text-xs m-4" variant={"default"}>
                <span className="text-xs">{content.content_type}</span>
              </Badge>
            </CardContent>
          </Card>
        ))}
        {filteredContent.length === 0 && (
          <div className="col-span-full text-center py-8 text-muted-foreground">
            No content found for the selected filter.
          </div>
        )}
      </div>
    </div>
  );
} 