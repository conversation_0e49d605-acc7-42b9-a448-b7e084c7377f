import { NextRequest, NextResponse } from 'next/server';
import { getCampaignBySlug } from '~/services/campaign';

export async function middleware(request: NextRequest) {
  // Get the campaign slug from the URL
  const slug = request.nextUrl.pathname.split('/').pop();
  
  if (!slug) {
    return NextResponse.next();
  }
  
  try {
    // Get the campaign
    const campaign = await getCampaignBySlug(slug);
    console.log('campaign?.has_reached_summary', campaign)
    // If the campaign has reached the summary and the URL ends with the slug
    // (indicating they're coming from the campaigns list view)
    if (campaign?.has_reached_summary && request.nextUrl.pathname.endsWith(slug)) {
      // Construct the path to the summary page
      const segments = request.nextUrl.pathname.split('/');
      const account = segments[segments.indexOf('campaigns') - 1];
      
      // Redirect to the summary page
      const url = new URL(`/home/<USER>/campaigns/${slug}/summary`, request.url);
      return NextResponse.redirect(url);
    }
  } catch (error) {
    console.error('Error in campaign middleware:', error);
  }
  
  return NextResponse.next();
}

// Only run this middleware on campaign detail routes
export const config = {
  matcher: '/home/<USER>/campaigns/:slug',
};