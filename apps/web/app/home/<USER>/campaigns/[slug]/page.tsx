import { redirect } from 'next/navigation';
import { getCampaignBySlug } from '~/services/campaign';

interface CampaignPageProps {
  params: {
    account: string;
    slug: string;
  };
}

export default async function CampaignPage({ params }: CampaignPageProps) {
  const { account, slug } = params;

  // Get the campaign
  const campaign = await getCampaignBySlug(slug);
  console.log('.has_reached_summary)', campaign)
  if (!campaign) {
    // If campaign not found, navigate to 404
    return redirect('/404');
  }
  
  // If the campaign has reached the summary page before, redirect to summary
  if (campaign.has_reached_summary) {
    return redirect(`/home/<USER>/campaigns/${slug}/summary`);
  }
  
  // Otherwise, redirect to the overview page (which is the default first step)
  return redirect(`/home/<USER>/campaigns/${slug}/overview`);
}