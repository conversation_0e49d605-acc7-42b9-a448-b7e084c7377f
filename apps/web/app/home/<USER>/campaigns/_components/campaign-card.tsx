'use client';
import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@kit/ui/card";
import { Badge } from "@kit/ui/badge";
import { Campaign } from "~/types/Campaign";
import { Button } from "@kit/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@kit/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@kit/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@kit/ui/dialog";
import { Input } from "@kit/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@kit/ui/select";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON>cil } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { deleteCampaign, updateCampaignField } from "~/services/campaign";

// Define a mapping for status to Tailwind CSS classes
const statusClasses: Record<string, string> = {
  Draft: "bg-gray-200 text-gray-800",
  Ready: "bg-green-200 text-green-800",
  "In Progress": "bg-yellow-200 text-yellow-800",
  Completed: "bg-blue-200 text-blue-800",
  Archived: "bg-red-200 text-red-800",
};

const CAMPAIGN_STATUS = ['Draft', 'Ready', 'In Progress', 'Completed', 'Archived'] as const;

interface EditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  fieldName: keyof Campaign;
  currentValue: string;
  onSave: (value: string) => Promise<void>;
  isLoading: boolean;
}

function EditDialog({
  isOpen,
  onOpenChange,
  title,
  fieldName,
  currentValue,
  onSave,
  isLoading
}: EditDialogProps) {
  const [value, setValue] = useState(currentValue);

  const handleSave = () => {
    onSave(value);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          {fieldName === 'status' ? (
            <Select
              value={value}
              onValueChange={setValue}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {CAMPAIGN_STATUS.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder={`Enter new ${fieldName}`}
              className="w-full"
            />
          )}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !value.trim() || value === currentValue}
          >
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function CampaignCard({ campaign, campaignSlug, accountSlug }: { campaign: Campaign, campaignSlug: string, accountSlug: string }) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editDialog, setEditDialog] = useState<{
    isOpen: boolean;
    field: keyof Campaign;
    title: string;
  }>({
    isOpen: false,
    field: 'name',
    title: '',
  });

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteCampaign(campaign.id);
      router.refresh();
    } catch (error) {
      console.error('Failed to delete campaign:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleFieldUpdate = async (value: string) => {
    try {
      setIsEditing(true);
      await updateCampaignField(campaign.id, {
        [editDialog.field]: value
      });
      setEditDialog(prev => ({ ...prev, isOpen: false }));
      router.refresh();
    } catch (error) {
      console.error(`Failed to update ${editDialog.field}:`, error);
    } finally {
      setIsEditing(false);
    }
  };

  const openEditDialog = (field: keyof Campaign, title: string) => {
    setEditDialog({
      isOpen: true,
      field,
      title,
    });
  };

  return (
    <Card className="max-w-2xl w-full h-48 relative">
      {/* Dropdown Menu positioned absolutely, outside the Link */}
      <div className="absolute top-4 right-4 z-10">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => openEditDialog('name', 'Rename Campaign')}
              className="flex items-center gap-2"
            >
              <Pencil className="h-4 w-4" /> Rename
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => openEditDialog('status', 'Edit Status')}
              className="flex items-center gap-2"
            >
              <Pencil className="h-4 w-4" /> Edit Status
            </DropdownMenuItem>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  className="flex items-center gap-2 text-destructive focus:text-destructive"
                  onSelect={(e) => e.preventDefault()} // Keep preventDefault for delete dialog trigger
                >
                  <Trash2 className="h-4 w-4" /> Delete
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Campaign</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete {campaign.name}? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Link href={`/home/<USER>/campaigns/${campaignSlug}`}>
        <CardHeader className="flex flex-col items-start justify-between space-y-0 pb-2"> {/* Adjusted padding */}
          <div className="flex justify-between w-full">
            <Badge variant="outline" className={`${statusClasses[campaign.status || "Draft"]}`}>
              {campaign.status}
            </Badge>
            {/* DropdownMenu removed from here */}
          </div>
           <div className="flex mt-5 flex-col">
            <CardTitle className="">{campaign.name}</CardTitle>
          </div>
        </CardHeader>

        <CardContent className="h-24 overflow-hidden pt-0"> {/* Adjusted padding */}
          {campaign.objective}
        </CardContent>
      </Link>

      <EditDialog
        isOpen={editDialog.isOpen}
        onOpenChange={(open) => setEditDialog(prev => ({ ...prev, isOpen: open }))}
        title={editDialog.title}
        fieldName={editDialog.field}
        currentValue={campaign[editDialog.field] as string}
        onSave={handleFieldUpdate}
        isLoading={isEditing}
      />
    </Card>
  );
}