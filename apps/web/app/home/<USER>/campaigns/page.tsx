//scaffold page for campaigns

import { getSupabaseServerClient } from "@kit/supabase/server-client";
import { createTeamAccountsApi } from "@kit/team-accounts/api";
import { getCampaigns } from "~/services/campaign";
import CampaignCard from "./_components/campaign-card";
import CreateCampaignDialog from "./_components/create-campaign-dialog";
import { If } from "@kit/ui/if";
import EmptyCampaigns from "./_components/empty-campaigns";
// import { redirect } from "next/navigation";

interface CampaignsPageProps {
    params: Promise<{ account: string }>;
    searchParams?: { campaignId?: string, type?: string };
  }

export default async function CampaignsPage(props: CampaignsPageProps) {
    const api = createTeamAccountsApi(getSupabaseServerClient());
    console.log(await props.params);
    const accountSlug = (await props.params).account;
    const data = await api.getTeamAccount(accountSlug);
    
    // Access query parameters
    const type = (await props.searchParams)?.type;

    const account = {
      id: data.id,
      name: data.name,
      pictureUrl: data.picture_url,
      slug: data.slug as string,
      primaryOwnerUserId: data.primary_owner_user_id,
    };
 
  const campaigns = await getCampaigns(account.id);
  const hasCampaigns = campaigns.length > 0;
  return (
    <div className="flex flex-col gap-4 p-12 relative">
      <If condition={hasCampaigns}>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-2xl">
            {campaigns.map((campaign, index) => (
              <CampaignCard  key={index} campaign={campaign} accountSlug={accountSlug} campaignSlug={campaign.slug} />
            ))}
          </div>
      </If>
      <If condition={!hasCampaigns}>
        
        <EmptyCampaigns accountSlug={accountSlug} />
      </If>
      <CreateCampaignDialog isNew={type === 'new'} />
    </div>
  )
}