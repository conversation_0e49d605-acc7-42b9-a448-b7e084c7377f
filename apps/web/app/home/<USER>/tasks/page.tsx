'use client';
import { TeamAccountLayoutPageHeader } from "../_components/layout"
import { PageBody } from "@kit/ui/page"
import { Trans } from "@kit/ui/trans"
import TasksDisplayArea from "./components/tasks-display-area"
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

export default function TaskPage() {
  const workspace = useTeamAccountWorkspace();

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={workspace.account.slug}
        title={<Trans i18nKey={'common:routes.dashboard'} />}
        description={'Tasks'}
      />
      <PageBody className="max-w-full">
        <TasksDisplayArea />
      </PageBody>
    </>
  )
}
