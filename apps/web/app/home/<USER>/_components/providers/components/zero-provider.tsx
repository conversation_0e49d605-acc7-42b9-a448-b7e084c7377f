'use client';
import { ZeroProvider } from '@rocicorp/zero/react';
import { useEffect } from 'react';
import { useZeroInstance } from '~/hooks/use-zero';

export function  AppZeroProvider({ children }: { children: React.ReactNode}) {
  const zero = useZeroInstance();

  useEffect(() => {
    if(zero) {
      const companyContentPreload = zero.query.company_content
        .limit(100)
        .preload({ttl: "forever"});
      
      const companyCampaignsPreload = zero.query.company_campaigns
        .limit(100)
        .preload({ttl: "forever"});
      
      const campaignTemplatesPreload = zero.query.campaign_templates
        .limit(100)
        .preload({ttl: "forever"});
        
      const postTemplatesPreload = zero.query.post_templates
        .limit(100)
        .preload({ttl: "forever"});

      const icpsPreload = zero.query.icps
        .limit(100)
        .preload({ttl: "forever"});

      const savedResearchPreload = zero.query.saved_research
        .limit(100)
        .preload({ttl: "forever"});
      
      const generatedResearch = zero.query.generated_research
        .limit(100)
        .preload({ttl: "forever"});
        

      const personasPreload = zero.query.personas
        .limit(100)
        .preload({ttl: "forever"});

      const productDocumentsPreload = zero.query.product_documents
        .limit(100)
        .preload({ttl: "forever"});
      
        const companyBrandPreload = zero.query.company_brand
          .limit(100)
          .preload({ttl: "forever"});

      const userCachePreload = zero.query.user_cache
          .limit(100)
          .preload({ttl: "forever"});

      const userProfilePreload = zero.query.ayrshare_user_profile
          .limit(100)
          .preload({ttl: "forever"});

        const socialProfilesPreload = zero.query.ayrshare_social_profiles
          .limit(100)
          .preload({ttl: "forever"});

          return () => {
            companyContentPreload.cleanup();
            companyCampaignsPreload.cleanup();
            campaignTemplatesPreload.cleanup();
            postTemplatesPreload.cleanup();
            icpsPreload.cleanup();
            savedResearchPreload.cleanup();
            generatedResearch.cleanup();
            personasPreload.cleanup();
            productDocumentsPreload.cleanup();
            companyBrandPreload.cleanup();
            userCachePreload.cleanup();
            userProfilePreload.cleanup();
            socialProfilesPreload.cleanup();
          }


    }
  
  }, [zero])

  return (
    <ZeroProvider zero={zero}>
      {children}
    </ZeroProvider>
  );
}