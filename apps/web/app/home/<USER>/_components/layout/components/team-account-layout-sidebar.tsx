'use client'
import type { User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  useSidebar,
} from '@kit/ui/shadcn-sidebar';
import { Button } from '@kit/ui/button';
import { PlusIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { cn } from '@kit/ui/utils';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Checkbox } from '@kit/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Spinner } from '@kit/ui/spinner';
import { createCampaign, getCampaigns } from '~/services/campaign';
import { createCampaignIdea } from '~/services/campaign-idea';
import { createCompanyContent } from '~/services/company-content';
import { Campaign } from '~/types/Campaign';

import { ProfileAccountDropdownContainer } from '~/components/personal-account-dropdown-container';
import { getTeamAccountSidebarConfig } from '~/config/team-account-navigation.config';
import { TeamAccountNotifications } from '../../navigation';
import { TeamAccountAccountsSelector } from '../../navigation';
import { TeamAccountLayoutSidebarNavigation } from './team-account-layout-sidebar-navigation';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief, extractCampaignBrief } from '~/utils/brief.util';

type AccountModel = {
  label: string | null;
  value: string | null;
  image: string | null;
};

export function TeamAccountLayoutSidebar(props: {
  account: string;
  accountId: string;
  accounts: AccountModel[];
  user: User;
}) {
  return (
    <SidebarContainer
      account={props.account}
      accountId={props.accountId}
      accounts={props.accounts}
      user={props.user}
    />
  );
}

function SidebarContainer(props: {
  account: string;
  accountId: string;
  accounts: AccountModel[];
  user: User;
}) {
  const { account, accounts, user } = props;
  const userId = user.id;
  const router = useRouter();
  const config = getTeamAccountSidebarConfig(account);
  const collapsible = config.sidebarCollapsedStyle;
  const [isOpen, setIsOpen] = useState(false);
  const { open } = useSidebar();
  
  // New state for form
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [objective, setObjective] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState<string>('');
  const [notPartOfCampaign, setNotPartOfCampaign] = useState(false);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const brandData = useBrandData(props.accountId);
  // Fetch campaigns when dialog opens
  useEffect(() => {
    if (isOpen) {
      const fetchCampaigns = async () => {
        try {
          const campaignsData = await getCampaigns(props.accountId);
          setCampaigns(campaignsData);
        } catch (error) {
          console.error('Error fetching campaigns:', error);
        }
      };
      fetchCampaigns();
    }
  }, [isOpen, props.accountId]);

  const handleCreatePost = async () => {
    if (!objective) {
      return;
    }

    setIsLoading(true);
    try {
      let campaignId = selectedCampaign;
      let campaignItem;

      // If no campaign is selected or "not part of campaign" is checked, create a new campaign
      if (notPartOfCampaign || !selectedCampaign) {
        const newCampaign = await createCampaign({
          company_id: props.accountId,
          name: objective,
          status: 'Draft',
          user_id: userId,
        });
        campaignId = newCampaign.id;
        campaignItem = newCampaign;
      } else {
        campaignItem = selectedCampaign;
      }

      //create creative brief
      const response = await fetch('/api/ai/brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: account,
          brand_brief: brandData.data ? extractBrandBrief(brandData.data) : 'No Brand Brief Provided',
          campaign_brief: extractCampaignBrief(campaignItem),
          //@ts-expect-error maps should work TODO: fix this
          product_info: campaigns.documents ? campaigns.documents?.map((doc: any) => `${doc.title}: ${doc.content}`).join('\n\n') : "No Product Info Provided",
          idea: objective,
          languages: ['en'], // Always set, defaults to 'en'
          supported_content_types: ['LinkedIn Post'],
          supported_channels: ['LinkedIn'],
        }),
      });
      const responseData = await response.json();
      const brief = responseData.creative_brief;

      // Create campaign idea
      const campaignIdea = await createCampaignIdea({
        campaign_id: campaignId,
        company_id: props.accountId,
        content: objective,
        languages: ['en'],
        brief,
      });

      // Create company content
      const companyContent = await createCompanyContent({
        company_id: props.accountId,
        campaign_id: campaignId,
        idea_id: campaignIdea.id,
        content_type: 'LinkedIn Post',
        language: 'en',
        content: objective,
        task_title: objective,
      });
      console.log("companyContent", companyContent);
      // Redirect to the post creation page
      const queryParams = new URLSearchParams();
      queryParams.set('objective', objective);
      queryParams.set('campaignId', campaignId);
      queryParams.set('ideaId', campaignIdea.id);
      queryParams.set('companyContentId', companyContent.id);

      router.push(`/home/<USER>/studio/${companyContent.id}?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error creating post:', error);
    } finally {
      setIsLoading(false);
      setIsOpen(false);
    }
  };

  return (
    <Sidebar collapsible={collapsible}>
      <SidebarHeader className={'h-16 justify-center'}>
        <div className={'flex flex-row items-center justify-between gap-x-3'}>
          <TeamAccountAccountsSelector
            userId={userId}
            selectedAccount={account}
            accounts={accounts}
          />

          <div className={'group-data-[minimized=true]:hidden'}>
            <TeamAccountNotifications
              userId={userId}
              accountId={props.accountId}
            />
          </div>
        </div>
      </SidebarHeader>
     
      <SidebarContent className={`mt-5 h-[calc(100%-160px)] overflow-y-auto`}>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size={open ? "default" : "icon"}
              className={open ? 'px-4 mx-4' : 'w-9 h-9 mx-auto p-0'}
            >
              <PlusIcon className="h-4 w-4" />
              {open && <span className="ml-2">Create</span>}
            </Button>
          </DialogTrigger>
          
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New</DialogTitle>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              {!showCreateForm ? (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    className={cn('h-32 space-y-2')}
                    onClick={() => setShowCreateForm(true)}
                  >
                    <div className="flex flex-col items-center justify-center">
                      <span className="mb-2 text-2xl">✍️</span>
                      <span className="font-medium">Create Single Post</span>
                      <span className="text-muted-foreground text-sm">
                        Create one post to get started
                      </span>
                    </div>
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    className={cn('h-32 space-y-2')}
                    onClick={() => {
                      router.push(`/home/<USER>/campaigns?type=new`);
                      setIsOpen(false);
                    }}
                  >
                    <div className="flex flex-col items-center justify-center">
                      <span className="mb-2 text-2xl">🚀</span>
                      <span className="font-medium">Create Campaign</span>
                      <span className="text-muted-foreground text-sm">
                        Plan and schedule multiple posts
                      </span>
                    </div>
                  </Button>
                </>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="objective">What do you want to achieve with this post?</Label>
                    <Input
                      id="objective"
                      value={objective}
                      onChange={(e) => setObjective(e.target.value)}
                      placeholder="Enter your objective..."
                    />
                  </div>

                  {campaigns.length > 0 && !notPartOfCampaign && (
                    <div className="space-y-2">
                      <Label htmlFor="campaign">Select Campaign</Label>
                      <Select value={selectedCampaign} onValueChange={setSelectedCampaign}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a campaign" />
                        </SelectTrigger>
                        <SelectContent>
                          {campaigns.map((campaign) => (
                            <SelectItem key={campaign.id} value={campaign.id}>
                              {campaign.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="notPartOfCampaign"
                      checked={notPartOfCampaign}
                      onCheckedChange={(checked) => {
                        setNotPartOfCampaign(checked as boolean);
                        if (checked) {
                          setSelectedCampaign('');
                        }
                      }}
                    />
                    <Label htmlFor="notPartOfCampaign">Not part of existing campaign</Label>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                      Back
                    </Button>
                    <Button 
                      onClick={handleCreatePost}
                      disabled={isLoading || !objective}
                    >
                      {isLoading ? (
                        <>
                          <Spinner className="mr-2 h-4 w-4" />
                          Creating...
                        </>
                      ) : (
                        'Continue'
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        <TeamAccountLayoutSidebarNavigation config={config} />
      </SidebarContent>

      <SidebarFooter>
        <SidebarContent>
          <ProfileAccountDropdownContainer user={props.user} />
        </SidebarContent>
      </SidebarFooter>
    </Sidebar>
  );
}
