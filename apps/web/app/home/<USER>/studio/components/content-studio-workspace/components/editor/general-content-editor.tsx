'use client';
import { Block } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { useEffect, useState, useCallback } from "react";
import { updateCompanyContent } from "~/services/company-content";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { createBlogPostBlocks, createInitialContent, createVideoBlock } from "~/utils/editor-util";
import { useBaseContent, useEditorContent, useImageContent, useVideoContent } from "../../context/ContentStudioContext";
import { useTheme } from "next-themes";
import { en as aiEn } from "@blocknote/xl-ai/locales";
import "@blocknote/xl-ai/style.css"; // add the AI stylesheet
import { createOpenAI } from '@ai-sdk/openai';
import { en } from "@blocknote/core/locales";
import {
  FormattingToolbar,
  FormattingToolbarController,
  getFormattingToolbarItems,
  useCreateBlockNote,
} from "@blocknote/react";
import {
  AIMenuController,
  AIToolbarButton,
  createAIExtension,
  createBlockNoteAIClient,
} from "@blocknote/xl-ai";
import { useZero } from "~/hooks/use-zero";
import { CompanyContent } from "~/types/company-content";
import { z } from "zod";
import { Textarea } from "@kit/ui/textarea";
import { Input } from "@kit/ui/input";
import { defaultContent } from "./default-content";
import { debounce } from "lodash";
export default function GeneralContentEditor({ 
  companyContent,
  editable = true
}: { 
  companyContent: CompanyContent,
  editable?: boolean
}) {
  // Track if this is the initial content load
  console.log("companyContent", companyContent);
  const { theme } = useTheme();
  const zero = useZero();  

  // const openai = createOpenAI({
  //   compatibility: 'strict', // strict mode, enable when using the OpenAI API
  //   apiKey: "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",
  //   baseURL: "https://openrouter.ai/api/v1",
  // });

  const client = createBlockNoteAIClient({
    apiKey: 'my-secret-token',
    baseURL: '/api/ai/stream-content',
  });

  const model = createOpenAI({
    ...client.getProviderSettings("openai")
  })("gpt-4o")

  const editor = useCreateBlockNote({
    // Register the AI extension
    dictionary: {
      ...en,
      ai: aiEn, // add default translations for the AI extension
    },
    extensions: [
      createAIExtension({
        model
      }),
    ],
  });

  const { setEditor } = useEditorContent();

  // Create debounced update function (saves every 5 seconds)
  const debouncedUpdateContent = useCallback(
    debounce((blocks: Block[]) => {
      zero.mutate.company_content.update({
        id: companyContent.id,
        values: {
          content_editor_template: blocks,
        }
      });
    }, 3000),
    [companyContent.id, zero]
  );

  // Handle initial content setup
  useEffect(() => {
    if (!editor) return;
    setEditor(editor);

    async function updateContent() {
      let blocks;
       if (companyContent.content_editor_template) {
        blocks = companyContent.content_editor_template;
      } else {
        blocks = defaultContent;
        // updateCompanyContent(companyContentId, {
        //   content_editor_template: blocks,
        // });
          // zero.mutate.company_content.update({
          //   id: companyContentId,
          //   content_editor_template: blocks,
          // });
      }
      editor.replaceBlocks(editor.document, blocks);
    }

    updateContent();
  }, []);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedUpdateContent.cancel();
    };
  }, [debouncedUpdateContent]);

  return (
    <>
    <BlockNoteView
      editor={editor} 
      editable={editable}
      theme={theme as "light" | "dark"} 
      formattingToolbar={false}
      onChange={() => {
        const blocks = editor.document;
        debouncedUpdateContent(blocks);
      }}
    >
       <AIMenuController />
       <FormattingToolbarWithAI />
    </BlockNoteView>
    </>
  );
}

function FormattingToolbarWithAI() {
  return (
    <FormattingToolbarController
      formattingToolbar={() => (
        <FormattingToolbar>
          {...getFormattingToolbarItems()}
          {/* Add the AI button */}
          <AIToolbarButton />
        </FormattingToolbar>
      )}
    />
  );
}
 