//scaffold

import { <PERSON><PERSON> } from "@kit/ui/button";
import { <PERSON><PERSON>, DialogTrigger, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@kit/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { PlusIcon } from "lucide-react";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState } from "react";
import { CompanyContent } from "~/types/company-content";
import { toast } from "@kit/ui/sonner";
// Note: For proper BlockNote integration, we need to import BlockNote
// import { useCreateBlockNote } from "@blocknote/react";

const PostScheduleSchema = z.object({
  taskStatus: z.string().optional(),
  // Note: Scheduling and approval fields commented out for future implementation
  // scheduleDate: z.date().optional(),
  // postNow: z.boolean(),
  // requireApproval: z.string().optional(),
});

type PostScheduleData = z.infer<typeof PostScheduleSchema>;

interface PostDialogProps {
  companyContent?: CompanyContent;
}

export default function PostDialog({ companyContent }: PostDialogProps) {
    const workspace = useTeamAccountWorkspace();
    const [isOpen, setIsOpen] = useState(false);
    const [isPosting, setIsPosting] = useState(false);

    const zero = useZero();

    // Note: For proper BlockNote integration, create editor instance here
    // const editor = useCreateBlockNote();

    // Function to convert BlockNote blocks to markdown
    const convertBlocksToMarkdown = async (blocks: any): Promise<string> => {
        try {
            // TODO: Replace with proper BlockNote conversion
            // const markdown = await editor.blocksToMarkdownLossy(blocks);
            // return markdown;

            // Temporary fallback - convert blocks to plain text
            if (Array.isArray(blocks)) {
                return blocks.map((block: any) => {
                    if (block.content) {
                        if (Array.isArray(block.content)) {
                            return block.content.map((item: any) => 
                                typeof item === 'string' ? item : item.text || ''
                            ).join('');
                        }
                        return typeof block.content === 'string' ? block.content : '';
                    }
                    return '';
                }).join('\n\n');
            }
            
            return typeof blocks === 'string' ? blocks : JSON.stringify(blocks);
        } catch (error) {
            console.error('Error converting blocks to markdown:', error);
            return '';
        }
    };

    const [accounts_memberships] = useZeroQuery(
        zero.query.accounts_memberships
        .where('account_id', workspace.account.id)
        ,
        {
          ttl: '10m'
        }
      );

      const [company_task_statuses] = useZeroQuery(
        zero.query.company_task_statuses
        ,{
            ttl: '10m'
        }
      );
      
      const [ayrshare_user_profile] = useZeroQuery(
        zero.query.ayrshare_user_profile
        ,{
            ttl: '10m'
        }
      );

    const form = useForm<PostScheduleData>({
        resolver: zodResolver(PostScheduleSchema),
        defaultValues: {
            taskStatus: "",
        },
    });

    const onSubmit = async (data: PostScheduleData) => {
        if (!companyContent?.content_editor_template || !companyContent?.channel) {
            toast.error('No content or channel specified for posting');
            return;
        }

        if (!ayrshare_user_profile || ayrshare_user_profile.length === 0) {
            toast.error('No Ayrshare profile found. Please connect your account first.');
            return;
        }

        const profile = ayrshare_user_profile[0];
        if (!profile?.profileKey) {
            toast.error('Invalid Ayrshare profile. Missing profile key.');
            return;
        }

        setIsPosting(true);
        
        try {
            // Convert BlockNote blocks to markdown
            const postContent = await convertBlocksToMarkdown(companyContent.content_editor_template);

            // Ensure platform name is correctly mapped for Ayrshare API
            let platform = companyContent.channel;
            
            // Map platform names to Ayrshare expected values
            const platformMapping: Record<string, string> = {
                'twitter': 'twitter',
                'x': 'twitter',  // In case it's stored as 'x'
                'linkedin': 'linkedin',
                'facebook': 'facebook',
                'instagram': 'instagram'
            };
            
            platform = platformMapping[platform.toLowerCase()] || platform;

            // Prepare the post data for Ayrshare API
            const postData = {
                post: postContent,
                platforms: [platform],
                mediaUrls: (companyContent.image_urls as string[]) || [],
                companyId: workspace.account.id,
                // Note: Scheduling and approval will be added later
                // scheduleDate: data.postNow ? undefined : data.scheduleDate?.toISOString(),
                // requiresApproval: data.requireApproval ? true : false,
            };

            console.log(`Posting to ${platform} via Ayrshare:`, {
                ...postData,
                profileKey: profile.profileKey ? 'present' : 'missing',
                userId: workspace.account.id
            });

            // Call the Ayrshare API
            const response = await fetch('/api/integrations/ayrshare/post', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(postData),
            });

            const result = await response.json();
            console.log('Ayrshare API response:', result);

            if (response.ok && result.success) {
                toast.success(`Post published successfully to ${platform}!`);
                
                // Extract post URL from the structured response
                let publishedUrl = '';
                if (result.primaryPostUrl) {
                    publishedUrl = result.primaryPostUrl;
                } else if (result.postUrls && result.postUrls[platform]) {
                    publishedUrl = result.postUrls[platform];
                }
                
                console.log('Extracted published URL:', publishedUrl);
                console.log('Available URLs by platform:', result.postUrls);
                
                // Update company content with publishing info and optional task status
                if (companyContent?.id) {
                    const updateValues: any = {
                        is_published: true,
                        published_at: Date.now(),
                        published_by: workspace.user.id,
                        published_url: publishedUrl
                    };
                    
                    // Add task status if selected
                    if (data.taskStatus) {
                        updateValues.status = data.taskStatus;
                    }
                    
                    await zero.mutate.company_content.update({
                        id: companyContent.id,
                        values: updateValues
                    });
                }
                
                setIsOpen(false);
                form.reset();
            } else {
                const errorMessage = result.error || result.message || 'Failed to publish post';
                toast.error(errorMessage);
                console.error('Ayrshare API error:', {
                    status: response.status,
                    statusText: response.statusText,
                    result
                });
            }
        } catch (error) {
            console.error('Error posting to Ayrshare:', error);
            toast.error('Failed to publish post');
        } finally {
            setIsPosting(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Post
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Publish Post</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        
                        {/* Task Status Dropdown */}
                        <FormField
                            control={form.control}
                            name="taskStatus"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Change Task Status (Optional)</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select task status" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {company_task_statuses?.map((status: any) => (
                                                <SelectItem key={status.id} value={status.name}>
                                                    {status.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Submit Button */}
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isPosting}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isPosting}>
                                {isPosting ? 'Posting...' : 'Post Now'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}