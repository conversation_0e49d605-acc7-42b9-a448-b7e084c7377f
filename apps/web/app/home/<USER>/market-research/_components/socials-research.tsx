'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Checkbox } from '@kit/ui/checkbox';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Loader2 } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';
import { useUser } from '@kit/supabase/hooks/use-user';

const TIME_FILTERS = [
  'Last 7 days',
  'Last 30 days',
  'Last 90 days'
];

const SOCIAL_PLATFORMS = [
  { value: 'twitter', label: 'Twitter/X' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'instagram', label: 'Instagram' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'tiktok', label: 'TikTok' },
  { value: 'youtube', label: 'YouTube' }
];

const RESEARCH_TYPES = [
  { value: 'sentiment-analysis', label: 'Sentiment Analysis' },
  { value: 'trending-hashtags', label: 'Trending Hashtags' },
  { value: 'influencer-mentions', label: 'Influencer Mentions' },
  { value: 'competitor-analysis', label: 'Competitor Analysis' }
];

interface SocialResearchFormData {
  icpId: string;
  personaId: string;
  platforms: string[];
  keywords: string;
  hashtags: string;
  timeFilter: string;
  type: string;
  includeInfluencers: boolean;
}

interface ICP {
  id: string;
  name: string;
}

interface Persona {
  id: string;
  name: string;
  role: string;
}

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: string;
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface SocialResearchProps {
  selectedResearch?: GeneratedResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  isGenerating?: boolean;
}

function useICPs(accountId: string) {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ['icps', accountId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('icps')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch ICPs: ${error.message}`);
      }

      return data as ICP[];
    },
    enabled: !!accountId,
  });
}

function usePersonas(accountId: string, icpId?: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['personas', accountId, icpId],
    queryFn: async () => {
      let query = supabase
        .from('personas')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (icpId) {
        query = query.eq('icp_id', icpId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch personas: ${error.message}`);
      }

      return data as Persona[];
    },
    enabled: !!accountId,
  });
}

export function SocialResearch({ selectedResearch, onResearchSaved, isGenerating }: SocialResearchProps) {
  const [pending, startTransition] = useTransition();
  const [formData, setFormData] = useState<SocialResearchFormData>({
    icpId: selectedResearch?.icp_id || '',
    personaId: selectedResearch?.persona_id || '',
    platforms: ['twitter', 'linkedin'],
    keywords: '',
    hashtags: '',
    timeFilter: 'Last 30 days',
    type: 'sentiment-analysis',
    includeInfluencers: false
  });

  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const { data: user } = useUser();
  const zero = useZero();

  const { data: icps = [], isLoading: icpsLoading } = useICPs(accountId);
  const { data: personas = [], isLoading: personasLoading } = usePersonas(accountId, formData.icpId);

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: ''
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handlePlatformChange = (platform: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      platforms: checked 
        ? [...prev.platforms, platform]
        : prev.platforms.filter(p => p !== platform)
    }));
  };

  const handleKeywordsChange = (value: string) => {
    setFormData(prev => ({ ...prev, keywords: value }));
  };

  const handleHashtagsChange = (value: string) => {
    setFormData(prev => ({ ...prev, hashtags: value }));
  };

  const handleTimeFilterChange = (value: string) => {
    setFormData(prev => ({ ...prev, timeFilter: value }));
  };

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }));
  };

  const handleIncludeInfluencersChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, includeInfluencers: checked }));
  };

  const handleSubmit = () => {
    if (!isFormValid || !zero || !user) return;

    startTransition(async () => {
      try {
        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);
        const researchTitle = `Social Media ${selectedType?.label || 'Research'} - ${selectedICP?.name || 'Unknown ICP'}`;
        const researchId = crypto.randomUUID();

        console.log('Creating social media research with Zero mutation...');

        zero.mutate.generated_research.insert({
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type,
          time_filter: formData.timeFilter,
          title: researchTitle,
          topic: `Social Media Research - Platforms: ${formData.platforms.join(', ')} - Keywords: ${formData.keywords || 'None'} - Hashtags: ${formData.hashtags || 'None'}`,
          created_by: user.id,
        });

        const placeholderResearch: GeneratedResearch = {
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type,
          time_filter: formData.timeFilter,
          title: researchTitle,
          results: [],
          content_suggestions: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          topic: `Social Media Research - Platforms: ${formData.platforms.join(', ')} - Keywords: ${formData.keywords || 'None'} - Hashtags: ${formData.hashtags || 'None'}`,
        };

        onResearchSaved?.(placeholderResearch);
        
        toast.success('Social media research generation started!', {
          description: 'Your social media research is being generated in the background.'
        });

      } catch (error) {
        console.error('Error generating social media research:', error);
        toast.error('Failed to generate social media research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.platforms.length > 0 && formData.timeFilter && formData.type;

  const selectedICP = icps.find(icp => icp.id === formData.icpId);
  const selectedPersona = formData.personaId === 'no-persona' ? null : personas.find(persona => persona.id === formData.personaId);
  const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);

  return (
    <div className="space-y-6">
      {/* ICP Selection */}
      <div className="space-y-2">
        <Label htmlFor="icp-select">Ideal Customer Profile (ICP)</Label>
        <Select 
          value={formData.icpId} 
          onValueChange={handleICPChange} 
          disabled={icpsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={icpsLoading ? "Loading ICPs..." : "Select an ICP"} />
          </SelectTrigger>
          <SelectContent>
            {icps.map(icp => (
              <SelectItem key={icp.id} value={icp.id}>
                {icp.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Persona Selection */}
      <div className="space-y-2">
        <Label htmlFor="persona-select">
          Persona <span className="text-muted-foreground">(Optional)</span>
        </Label>
        <Select 
          value={formData.personaId} 
          onValueChange={handlePersonaChange} 
          disabled={personasLoading || !formData.icpId}
        >
          <SelectTrigger>
            <SelectValue 
              placeholder={
                !formData.icpId 
                  ? "Select an ICP first" 
                  : personasLoading 
                    ? "Loading personas..." 
                    : "Select a persona (optional)"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="no-persona">
              <span className="text-muted-foreground">No specific persona</span>
            </SelectItem>
            {personas.map(persona => (
              <SelectItem key={persona.id} value={persona.id}>
                <div className="flex flex-col">
                  <span>{persona.name}</span>
                  <span className="text-xs text-muted-foreground">{persona.role}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Platform Selection */}
      <div className="space-y-3">
        <Label>Social Media Platforms</Label>
        <div className="grid grid-cols-2 gap-3">
          {SOCIAL_PLATFORMS.map(platform => (
            <div key={platform.value} className="flex items-center space-x-2">
              <Checkbox
                id={platform.value}
                checked={formData.platforms.includes(platform.value)}
                onCheckedChange={(checked) => handlePlatformChange(platform.value, checked as boolean)}
              />
              <Label htmlFor={platform.value} className="cursor-pointer">
                {platform.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Keywords Input */}
      <div className="space-y-2">
        <Label htmlFor="keywords-input">Keywords</Label>
        <Input
          id="keywords-input"
          type="text"
          placeholder="Enter keywords separated by commas (e.g., SaaS, productivity, automation)"
          value={formData.keywords}
          onChange={(e) => handleKeywordsChange(e.target.value)}
          className="w-full"
        />
      </div>

      {/* Hashtags Input */}
      <div className="space-y-2">
        <Label htmlFor="hashtags-input">Hashtags</Label>
        <Input
          id="hashtags-input"
          type="text"
          placeholder="Enter hashtags separated by commas (e.g., #SaaS, #startup, #productivity)"
          value={formData.hashtags}
          onChange={(e) => handleHashtagsChange(e.target.value)}
          className="w-full"
        />
      </div>

      {/* Research Type */}
      <div className="space-y-2">
        <Label htmlFor="research-type">Research Type</Label>
        <Select 
          value={formData.type} 
          onValueChange={handleTypeChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select research type" />
          </SelectTrigger>
          <SelectContent>
            {RESEARCH_TYPES.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Time Filter */}
      <div className="space-y-2">
        <Label htmlFor="time-filter">Time Filter</Label>
        <Select 
          value={formData.timeFilter} 
          onValueChange={handleTimeFilterChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time period" />
          </SelectTrigger>
          <SelectContent>
            {TIME_FILTERS.map(filter => (
              <SelectItem key={filter} value={filter}>
                {filter}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Include Influencers */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="include-influencers"
          checked={formData.includeInfluencers}
          onCheckedChange={(checked) => handleIncludeInfluencersChange(checked as boolean)}
        />
        <Label htmlFor="include-influencers" className="cursor-pointer">
          Include influencer analysis
        </Label>
      </div>

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={true}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Generating Social Media Research...
          </div>
        ) : (
          'Beta - Coming Soon'
        )}
      </Button>

      {/* Selected Configuration Summary */}
      {(selectedICP || selectedPersona || selectedType) && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Research Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedICP && (
              <Badge variant="outline">ICP: {selectedICP.name}</Badge>
            )}
            {selectedPersona && (
              <Badge variant="outline">Persona: {selectedPersona.name}</Badge>
            )}
            {selectedType && (
              <Badge variant="outline">Type: {selectedType.label}</Badge>
            )}
            {formData.platforms.length > 0 && (
              <Badge variant="outline">Platforms: {formData.platforms.join(', ')}</Badge>
            )}
            {formData.timeFilter && (
              <Badge variant="outline">Period: {formData.timeFilter}</Badge>
            )}
            {formData.keywords && (
              <Badge variant="outline">Keywords: {formData.keywords}</Badge>
            )}
            {formData.hashtags && (
              <Badge variant="outline">Hashtags: {formData.hashtags}</Badge>
            )}
            {formData.includeInfluencers && (
              <Badge variant="outline">Influencer Analysis: Yes</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

