'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Checkbox } from '@kit/ui/checkbox';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Loader2, Plus, X } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';
import { useUser } from '@kit/supabase/hooks/use-user';

const FIELD_TYPES = [
  { value: 'text', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'boolean', label: 'True/False' }
];

interface FieldToFind {
  name: string;
  type: 'text' | 'number' | 'boolean';
}

interface SiteSearchFormData {
  icpId: string;
  personaId: string;
  urls: string[];
  fieldsToFind: FieldToFind[];
  instructions: string;
  allowWebSearch: boolean;
  agentMode: boolean;
}

interface ICP {
  id: string;
  name: string;
}

interface Persona {
  id: string;
  name: string;
  role: string;
}

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: string;
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface SiteResearch {
  id: string;
  company_id: string;
  created_at: number;
  icps: string[];
  personal: string[];
  urls: string[];
  instruction: string;
  schema: Record<string, any>;
  enable_web_search: boolean;
  agent_mode: boolean;
  is_generating: boolean;
  error_generating: boolean;
  results: Record<string, any>[];
}

interface SiteSearchProps {
  selectedResearch?: GeneratedResearch | null;
  selectedSiteResearch?: SiteResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  onSiteResearchSaved?: (research: SiteResearch) => void;
  isGenerating?: boolean;
}

function useICPs(accountId: string) {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ['icps', accountId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('icps')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch ICPs: ${error.message}`);
      }

      return data as ICP[];
    },
    enabled: !!accountId,
  });
}

function usePersonas(accountId: string, icpId?: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['personas', accountId, icpId],
    queryFn: async () => {
      let query = supabase
        .from('personas')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (icpId) {
        query = query.eq('icp_id', icpId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch personas: ${error.message}`);
      }

      return data as Persona[];
    },
    enabled: !!accountId,
  });
}

export function SiteSearch({ selectedResearch, selectedSiteResearch, onResearchSaved, onSiteResearchSaved, isGenerating }: SiteSearchProps) {
  const [pending, startTransition] = useTransition();
  const [newUrl, setNewUrl] = useState('');
  const [formData, setFormData] = useState<SiteSearchFormData>({
    icpId: selectedResearch?.icp_id || '',
    personaId: selectedResearch?.persona_id || '',
    urls: [],
    fieldsToFind: [{ name: '', type: 'text' }],
    instructions: '',
    allowWebSearch: false,
    agentMode: false
  });

  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const { data: user } = useUser();
  const zero = useZero();

  const { data: icps = [], isLoading: icpsLoading } = useICPs(accountId);
  const { data: personas = [], isLoading: personasLoading } = usePersonas(accountId, formData.icpId);

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: ''
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handleAddUrl = () => {
    if (newUrl.trim()) {
      setFormData(prev => ({
        ...prev,
        urls: [...prev.urls, newUrl.trim()]
      }));
      setNewUrl('');
    }
  };

  const handleRemoveUrl = (index: number) => {
    setFormData(prev => ({
      ...prev,
      urls: prev.urls.filter((_, i) => i !== index)
    }));
  };

  const handleFieldNameChange = (index: number, name: string) => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: prev.fieldsToFind.map((field, i) => 
        i === index ? { ...field, name } : field
      )
    }));
  };

  const handleFieldTypeChange = (index: number, type: 'text' | 'number' | 'boolean') => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: prev.fieldsToFind.map((field, i) => 
        i === index ? { ...field, type } : field
      )
    }));
  };

  const handleAddField = () => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: [...prev.fieldsToFind, { name: '', type: 'text' }]
    }));
  };

  const handleRemoveField = (index: number) => {
    setFormData(prev => ({
      ...prev,
      fieldsToFind: prev.fieldsToFind.filter((_, i) => i !== index)
    }));
  };

  const handleInstructionsChange = (value: string) => {
    setFormData(prev => ({ ...prev, instructions: value }));
  };

  const handleAllowWebSearchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, allowWebSearch: checked }));
  };

  const handleAgentModeChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, agentMode: checked }));
  };

  const handleSubmit = () => {
    if (!isFormValid || !zero || !user) return;

    startTransition(async () => {
      try {
        // Transform form data to expected API format
        const schema: Record<string, any> = {};
        
        // Convert fieldsToFind to schema format
        formData.fieldsToFind.forEach(field => {
          if (field.name.trim()) {
            let jsonSchemaType: string;
            switch (field.type) {
              case 'text':
                jsonSchemaType = 'string';
                break;
              case 'number':
                jsonSchemaType = 'number';
                break;
              case 'boolean':
                jsonSchemaType = 'boolean';
                break;
              default:
                jsonSchemaType = 'string';
            }
            
            schema[field.name] = {
              type: jsonSchemaType
            };
          }
        });

        // Prepare the API payload
        const apiPayload = {
          urls: formData.urls,
          schema: schema,
          prompt: formData.instructions || '',
          enableWebSearch: formData.allowWebSearch,
          ...(formData.agentMode && {
            agent: {
              model: "FIRE-1"
            }
          })
        };

        // Log the transformed data
        console.log('API Payload:', JSON.stringify(apiPayload, null, 2));
        console.log('Form Data:', formData);

        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        const researchTitle = `Site Analysis - ${selectedICP?.name || 'Unknown ICP'}`;
        const researchId = crypto.randomUUID();

        console.log('Creating site search research with Zero mutation...');
        zero.mutate.site_research.insert({
          id: researchId,
          values: {
            company_id: accountId,
            icps: [formData.icpId],
            personal: [formData.personaId],
            urls: formData.urls,
            instruction: formData.instructions,
            schema: schema,
            enable_web_search: formData.allowWebSearch,
            agent_mode: formData.agentMode,
            is_generating: true,
            error_generating: false,
            results: [],
          }
        });

        // Create a placeholder SiteResearch object
        const placeholderSiteResearch: SiteResearch = {
          id: researchId,
          company_id: accountId,
          created_at: Date.now(),
          icps: [formData.icpId],
          personal: [formData.personaId],
          urls: formData.urls,
          instruction: formData.instructions,
          schema: schema,
          enable_web_search: formData.allowWebSearch,
          agent_mode: formData.agentMode,
          is_generating: true,
          error_generating: false,
          results: [],
        };

        onSiteResearchSaved?.(placeholderSiteResearch);
        
        toast.success('Site analysis generation started!', {
          description: 'Your site analysis is being generated in the background.'
        });

      } catch (error) {
        console.error('Error generating site research:', error);
        toast.error('Failed to generate site research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.urls.length > 0 && formData.fieldsToFind.some(field => field.name.trim());

  const selectedICP = icps.find(icp => icp.id === formData.icpId);
  const selectedPersona = formData.personaId === 'no-persona' ? null : personas.find(persona => persona.id === formData.personaId);

  return (
    <div className="space-y-6">
      {/* ICP Selection */}
      <div className="space-y-2">
        <Label htmlFor="icp-select">Ideal Customer Profile (ICP)</Label>
        <p className="text-xs text-muted-foreground">Select the target customer profile for this analysis</p>
        <Select 
          value={formData.icpId} 
          onValueChange={handleICPChange} 
          disabled={icpsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={icpsLoading ? "Loading ICPs..." : "Select an ICP"} />
          </SelectTrigger>
          <SelectContent>
            {icps.map(icp => (
              <SelectItem key={icp.id} value={icp.id}>
                {icp.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Persona Selection */}
      <div className="space-y-2">
        <Label htmlFor="persona-select">
          Persona <span className="text-muted-foreground">(Optional)</span>
        </Label>
        <p className="text-xs text-muted-foreground">Choose a specific persona within your ICP to focus the analysis</p>
        <Select 
          value={formData.personaId} 
          onValueChange={handlePersonaChange} 
          disabled={personasLoading || !formData.icpId}
        >
          <SelectTrigger>
            <SelectValue 
              placeholder={
                !formData.icpId 
                  ? "Select an ICP first" 
                  : personasLoading 
                    ? "Loading personas..." 
                    : "Select a persona (optional)"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="no-persona">
              <span className="text-muted-foreground">No specific persona</span>
            </SelectItem>
            {personas.map(persona => (
              <SelectItem key={persona.id} value={persona.id}>
                <div className="flex flex-col">
                  <span>{persona.name}</span>
                  <span className="text-xs text-muted-foreground">{persona.role}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* URLs to Analyze */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="url-input">URLs to Analyze</Label>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={handleAddUrl}
            disabled={!newUrl.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">Enter URLs one at a time to analyze and extract information from</p>
        <Input
          id="url-input"
          type="url"
          placeholder="https://example.com"
          value={newUrl}
          onChange={(e) => setNewUrl(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleAddUrl();
            }
          }}
        />
        
        {/* Display added URLs */}
        {formData.urls.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Added URLs:</Label>
            <div className="space-y-1">
              {formData.urls.map((url, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                  <span className="text-sm truncate">{url}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveUrl(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Fields to Find */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Fields to Find</Label>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={handleAddField}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Field
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">Define the specific data fields you want to extract from each URL</p>
        
        <div className="space-y-3">
          {formData.fieldsToFind.map((field, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="flex-1">
                <Input
                  placeholder="Field name (e.g., Company Name)"
                  value={field.name}
                  onChange={(e) => handleFieldNameChange(index, e.target.value)}
                />
              </div>
              <div className="w-32">
                <Select
                  value={field.type}
                  onValueChange={(value) => handleFieldTypeChange(index, value as 'text' | 'number' | 'boolean')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FIELD_TYPES.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {formData.fieldsToFind.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveField(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="space-y-2">
        <Label htmlFor="instructions">Instructions</Label>
        <p className="text-xs text-muted-foreground">Provide specific instructions for how to analyze the websites and extract the data</p>
        <textarea
          id="instructions"
          placeholder="Enter any specific instructions for the analysis..."
          value={formData.instructions}
          onChange={(e) => handleInstructionsChange(e.target.value)}
          className="w-full min-h-[80px] p-3 border border-input rounded-md bg-background text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          rows={3}
        />
      </div>

      {/* Allow Web Search */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="allow-web-search"
            checked={formData.allowWebSearch}
            onCheckedChange={(checked) => handleAllowWebSearchChange(checked as boolean)}
          />
          <Label htmlFor="allow-web-search" className="cursor-pointer">
            Allow Web Search
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">Enable additional web searches to gather more context and information</p>
      </div>

      {/* Agent Mode */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="agent-mode"
            checked={formData.agentMode}
            onCheckedChange={(checked) => handleAgentModeChange(checked as boolean)}
          />
          <Label htmlFor="agent-mode" className="cursor-pointer">
            Agent Mode
          </Label>
        </div>
        <p className="text-xs text-muted-foreground">Enable advanced AI agent capabilities for more thorough analysis</p>
      </div>

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={!isFormValid || pending || isGenerating}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Analyzing Sites...
          </div>
        ) : (
          'Start Site Analysis'
        )}  
      </Button>

      {/* Selected Configuration Summary */}
      {(selectedICP || selectedPersona || formData.urls.length > 0) && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Analysis Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedICP && (
              <Badge variant="outline">ICP: {selectedICP.name}</Badge>
            )}
            {selectedPersona && (
              <Badge variant="outline">Persona: {selectedPersona.name}</Badge>
            )}
            {formData.urls.length > 0 && (
              <Badge variant="outline">URLs: {formData.urls.length}</Badge>
            )}
            {formData.fieldsToFind.filter(field => field.name.trim()).length > 0 && (
              <Badge variant="outline">Fields: {formData.fieldsToFind.filter(field => field.name.trim()).length}</Badge>
            )}
            {formData.allowWebSearch && (
              <Badge variant="outline">Web Search: Enabled</Badge>
            )}
            {formData.agentMode && (
              <Badge variant="outline">Agent Mode: Enabled</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

