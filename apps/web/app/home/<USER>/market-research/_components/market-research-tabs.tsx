'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { MarketResearchForm } from './market-research-form';
import { SavedResearch } from './saved-research';
import { ResearchSidebar } from './research-sidebar';

// Define the interface for generated research
interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: string;
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface SiteResearch {
  id: string;
  company_id: string;
  created_at: number;
  icps: string[];
  personal: string[];
  urls: string[];
  instruction: string;
  schema: Record<string, any>;
  enable_web_search: boolean;
  agent_mode: boolean;
  is_generating: boolean;
  error_generating: boolean;
  results: Record<string, any>[];
}

export function MarketResearchTabs() {
  const [activeTab, setActiveTab] = useState('generate');
  const [selectedResearch, setSelectedResearch] = useState<GeneratedResearch | null>(null);
  const [selectedSiteResearch, setSelectedSiteResearch] = useState<SiteResearch | null>(null);

  const handleSelectResearch = (research: GeneratedResearch | null) => {
    setSelectedResearch(research);
    setSelectedSiteResearch(null);
    if (research) {
      setActiveTab('generate');
    }
  };

  const handleSelectSiteResearch = (siteResearch: SiteResearch | null) => {
    setSelectedSiteResearch(siteResearch);
    setSelectedResearch(null);
    if (siteResearch) {
      setActiveTab('generate');
    }
  };

  const handleNewResearch = () => {
    setSelectedResearch(null);
    setSelectedSiteResearch(null);
    setActiveTab('generate');
  };

  return (
    <div className="">
    
      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col">
          <div className="border-b px-6 py-4">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="generate">
                <Trans i18nKey="marketResearch:generateTab" defaults="Generate Research" />
              </TabsTrigger>
              <TabsTrigger value="saved">
                <Trans i18nKey="marketResearch:savedTab" defaults="Saved Research" />
              </TabsTrigger>
            </TabsList>
          </div>
          
          <div className="flex-1 overflow-auto">
            <TabsContent value="generate" className="mt-0 h-full">
              <div className="flex flex h-[calc(100vh-200px)] w-full">
                <div>
              {/* Sidebar - only show on generate tab */}
              {activeTab === 'generate' && (
                    <ResearchSidebar
                      selectedResearchId={selectedResearch?.id}
                      selectedSiteResearchId={selectedSiteResearch?.id}
                      onSelectResearch={handleSelectResearch}
                      onSelectSiteResearch={handleSelectSiteResearch}
                      onNewResearch={handleNewResearch}
                    />
                  )}
                </div>
                <div className='p-6 w-full h-full'>
                <MarketResearchForm 
                  selectedResearch={selectedResearch}
                  selectedSiteResearch={selectedSiteResearch}
                  onResearchSaved={handleSelectResearch}
                  onSiteResearchSaved={handleSelectSiteResearch}
                />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="saved" className="mt-0 h-full">
              <div className="p-6">
                <SavedResearch />
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
} 