'use client';

import { useState, useTransition, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Label } from '@kit/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { toast } from '@kit/ui/sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Loader2 } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useUser } from '@kit/supabase/hooks/use-user';


import { If } from '@kit/ui/if';
import { WebResearch } from './web-research';
import { SocialResearch } from './socials-research';
import { SiteSearch } from './site-search';
import { ResearchResults } from './research-results';
import { ContentSuggestions } from './content-suggestions';
import { SiteResearchResults } from './site-research-results';

const TIME_FILTERS = [
  'Last 3 months',
  'Last 6 months', 
  'Last 12 months'
];

const RESEARCH_TYPES = [
  { value: 'pain-points', label: 'Pain Points' },
  { value: 'trending-topics', label: 'Trending Topics' },
  { value: 'recent-news', label: 'Recent News' }
];

const SEARCH_TYPES = [
  { value: 'web-search', label: 'Web Search' },
  { value: 'social-media-search', label: 'Social Media Search' },
  { value: 'search-by-url', label: 'Search By URL' }
];

interface FormData {
  icpId: string;
  personaId: string;
  timeFilter: string;
  type: string;
  topic: string;
  searchType: string;
}

interface ResearchResult {
  title: string;
  description: string;
  source: string;
  source_url: string;
  relevance_score: number;
}

interface ContentSuggestion {
  topic: string;
  description: string;
  content_type: string;
  target_audience: string;
}

interface ICP {
  id: string;
  name: string;
}

interface Persona {
  id: string;
  name: string;
  role: string;
}

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: string;
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface SiteResearch {
  id: string;
  company_id: string;
  created_at: number;
  icps: string[];
  personal: string[];
  urls: string[];
  instruction: string;
  schema: Record<string, any>;
  enable_web_search: boolean;
  agent_mode: boolean;
  is_generating: boolean;
  error_generating: boolean;
  results: Record<string, any>[];
}

interface MarketResearchFormProps {
  selectedResearch?: GeneratedResearch | null;
  selectedSiteResearch?: SiteResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  onSiteResearchSaved?: (research: SiteResearch) => void;
}

function useICPs(accountId: string) {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ['icps', accountId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('icps')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch ICPs: ${error.message}`);
      }

      return data as ICP[];
    },
    enabled: !!accountId,
  });
}

function usePersonas(accountId: string, icpId?: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['personas', accountId, icpId],
    queryFn: async () => {

      let query = supabase
        .from('personas')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      // Filter by ICP if selected
      if (icpId) {
        query = query.eq('icp_id', icpId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch personas: ${error.message}`);
      }

      return data as Persona[];
    },
    enabled: !!accountId,
  });
}

export function MarketResearchForm({ selectedResearch, selectedSiteResearch, onResearchSaved, onSiteResearchSaved }: MarketResearchFormProps) {
  const [pending, startTransition] = useTransition();
  const [results, setResults] = useState<ResearchResult[]>([]);
  const [contentSuggestions, setContentSuggestions] = useState<ContentSuggestion[]>([]);
  const [siteResults, setSiteResults] = useState<Record<string, any>[]>([]);
  const [currentResearchId, setCurrentResearchId] = useState<string | null>(null);
  const [currentSiteResearchId, setCurrentSiteResearchId] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({
    icpId: '',
    personaId: '',
    timeFilter: '',
    type: '',
    topic: '',
    searchType: 'web-search'
  });

  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const { data: user } = useUser();
  const zero = useZero();



  // Watch for real-time updates to the current research
  const [liveResearch] = useZeroQuery(
    currentResearchId && zero 
      ? zero.query.generated_research.where('id', currentResearchId).one()
      : zero?.query.generated_research.where('id', 'never-match').one()
  );

  // Watch for real-time updates to the current site research
  const [liveSiteResearch] = useZeroQuery(
    currentSiteResearchId && zero 
      ? zero.query.site_research.where('id', currentSiteResearchId).one()
      : zero?.query.site_research.where('id', 'never-match').one()
  );


  const { data: icps = [], isLoading: icpsLoading } = useICPs(accountId);
  const { data: personas = [], isLoading: personasLoading } = usePersonas(accountId, formData.icpId);

  // Update results when live research data changes
  useEffect(() => {
    if (liveResearch && liveResearch.id === currentResearchId) {
      
      // Update results and content suggestions from the live data
      if (liveResearch.results && Array.isArray(liveResearch.results)) {
        setResults(liveResearch.results as ResearchResult[]);
      }
      
      if (liveResearch.content_suggestions && Array.isArray(liveResearch.content_suggestions)) {
        setContentSuggestions(liveResearch.content_suggestions as ContentSuggestion[]);
      }

      // If research is no longer generating, show success message
      if (liveResearch.is_generating === false && results.length === 0 && 
          liveResearch.results && Array.isArray(liveResearch.results) && liveResearch.results.length > 0) {
        const contentSuggestionsCount = liveResearch.content_suggestions && Array.isArray(liveResearch.content_suggestions) 
          ? liveResearch.content_suggestions.length 
          : 0;
        toast.success('Market research completed!', {
          description: `Found ${liveResearch.results.length} research insights and ${contentSuggestionsCount} content suggestions.`
        });
      }
    }
  }, [liveResearch, currentResearchId, results.length]);

  // Update site research results when live site research data changes
  useEffect(() => {
    if (liveSiteResearch && liveSiteResearch.id === currentSiteResearchId) {
      
      // Update site results from the live data
      let liveResultsArray: Record<string, any>[] = [];
      if (liveSiteResearch.results) {
        if (Array.isArray(liveSiteResearch.results)) {
          liveResultsArray = liveSiteResearch.results;
        } else if (typeof liveSiteResearch.results === 'object') {
          // If results is a single object, wrap it in an array
          liveResultsArray = [liveSiteResearch.results];
        }
      }
      setSiteResults(liveResultsArray);

      // If site research is no longer generating, show success message
      if (liveSiteResearch.is_generating === false && siteResults.length === 0 && 
          liveSiteResearch.results && Array.isArray(liveSiteResearch.results) && liveSiteResearch.results.length > 0) {
        const urlsCount = Array.isArray(liveSiteResearch.urls) ? liveSiteResearch.urls.length : 0;
        toast.success('Site analysis completed!', {
          description: `Analyzed ${urlsCount} URLs and extracted ${liveSiteResearch.results.length} data records.`
        });
      }
    }
  }, [liveSiteResearch, currentSiteResearchId, siteResults.length]);

  // Load selected research data when selectedResearch changes
  useEffect(() => {
    if (selectedResearch) {
      setFormData({
        icpId: selectedResearch.icp_id,
        personaId: selectedResearch.persona_id || 'no-persona',
        timeFilter: selectedResearch.time_filter,
        type: selectedResearch.research_type,
        topic: selectedResearch.topic || '',
        searchType: 'web-search',
      });
      setResults(selectedResearch.results || []);
      setContentSuggestions(selectedResearch.content_suggestions || []);
      setCurrentResearchId(selectedResearch.id);
      setCurrentSiteResearchId(null);
      setSiteResults([]);
    } else if (selectedSiteResearch) {
      // Load site research data
      const icpId = Array.isArray(selectedSiteResearch.icps) && selectedSiteResearch.icps.length > 0 
        ? String(selectedSiteResearch.icps[0]) 
        : '';
      const personaId = Array.isArray(selectedSiteResearch.personal) && selectedSiteResearch.personal.length > 0 
        ? String(selectedSiteResearch.personal[0]) 
        : 'no-persona';
      
      setFormData({
        icpId: icpId,
        personaId: personaId,
        timeFilter: '',
        type: '',
        topic: '',
        searchType: 'search-by-url',
      });
      setResults([]);
      setContentSuggestions([]);
      // Convert results to array if it's an object
      let siteResultsArray: Record<string, any>[] = [];
      if (selectedSiteResearch.results) {
        if (Array.isArray(selectedSiteResearch.results)) {
          siteResultsArray = selectedSiteResearch.results;
        } else if (typeof selectedSiteResearch.results === 'object') {
          // If results is a single object, wrap it in an array
          siteResultsArray = [selectedSiteResearch.results];
        }
      }
      setSiteResults(siteResultsArray);
      setCurrentResearchId(null);
      setCurrentSiteResearchId(selectedSiteResearch.id);
    } else {
      // Reset form for new research
      setFormData({
        icpId: '',
        personaId: '',
        timeFilter: '',
        type: '',
        topic: '',
        searchType: 'web-search'
      });
      setResults([]);
      setContentSuggestions([]);
      setSiteResults([]);
      setCurrentResearchId(null);
      setCurrentSiteResearchId(null);
    }
  }, [selectedResearch, selectedSiteResearch]);



  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: '' // Reset persona when ICP changes
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handleTimeFilterChange = (value: string) => {
    setFormData(prev => ({ ...prev, timeFilter: value }));
  };

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }));
  };

  const handleTopicChange = (value: string) => {
    setFormData(prev => ({ ...prev, topic: value }));
  };

  const handleSearchTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, searchType: value }));
  };


  
  const handleSubmit = () => {
    if (!isFormValid || !zero || !user) return;

    startTransition(async () => {
      try {
        // Get the full ICP and persona data
        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        // const selectedPersona = personas.find(persona => persona.id === formData.personaId);
        
        // Generate a title for the research
        const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);
        const researchTitle = `${selectedType?.label || 'Research'} - ${selectedICP?.name || 'Unknown ICP'}`;

        // Generate a unique ID for this research
        const researchId = crypto.randomUUID();

        console.log('Creating market research with Zero mutation...');

        // Use Zero mutation to create the research record
        zero.mutate.generated_research.insert({
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type,
          time_filter: formData.timeFilter,
          title: researchTitle,
          topic: formData.topic,
          created_by: user.id,
        });

        // Clear previous results and show loading state
        setResults([]);
        setContentSuggestions([]);
        setCurrentResearchId(researchId);

        // Call the onResearchSaved callback with a placeholder record
        const placeholderResearch: GeneratedResearch = {
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type as 'pain-points' | 'trending-topics' | 'recent-news',
          time_filter: formData.timeFilter,
          title: researchTitle,
          results: [],
          content_suggestions: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          topic: formData.topic,
        };

        onResearchSaved?.(placeholderResearch);
        
        toast.success('Market research generation started!', {
          description: 'Your research is being generated in the background. Results will appear shortly.'
        });

      } catch (error) {
        console.error('Error generating market research:', error);
        toast.error('Failed to generate market research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.timeFilter && formData.type;
  const isGenerating = liveResearch?.is_generating === true || liveSiteResearch?.is_generating === true;

  const selectedICP = icps.find(icp => icp.id === formData.icpId);
  const selectedPersona = formData.personaId === 'no-persona' ? null : personas.find(persona => persona.id === formData.personaId);
  const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);

  return (
    <div className="space-y-6">
      {/* Show research title if viewing existing research */}
      {selectedResearch && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold">{selectedResearch.title}</h2>
          <p className="text-sm text-muted-foreground">
            Created on {new Date(selectedResearch.created_at).toLocaleDateString()}
          </p>
        </div>
      )}

      {/* Show site research title if viewing existing site research */}
      {selectedSiteResearch && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold">Site Analysis</h2>
          <p className="text-sm text-muted-foreground">
            Created on {new Date(selectedSiteResearch.created_at).toLocaleDateString()} • 
            {Array.isArray(selectedSiteResearch.urls) ? selectedSiteResearch.urls.length : 0} URLs analyzed
          </p>
        </div>
      )}

      {/* Show generating status */}
      {isGenerating && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  {liveSiteResearch?.is_generating ? 'Site Analysis' : 'Research Generation'} in Progress
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Your {liveSiteResearch?.is_generating ? 'site analysis' : 'market research'} is being generated. Results will appear here automatically.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Research Configuration Form - only show when creating new research */}
      {!selectedResearch && !selectedSiteResearch && (
        <Card className="w-full">
          <CardHeader>
            <div className="flex justify-between items-start gap-4">
              <div className="flex-1">
                <CardTitle>Generate New Research</CardTitle>
                <CardDescription>Fill in the form fields below to get insights about your ICP or persona relevant to your business.</CardDescription>
              </div>
              <div className="flex-shrink-0 min-w-[200px]">
                <div className="space-y-2">
                  <Label htmlFor="search-type" className="text-sm font-medium">Search Method</Label>
                  <Select 
                    value={formData.searchType} 
                    onValueChange={handleSearchTypeChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select search method" />
                    </SelectTrigger>
                    <SelectContent>
                      {SEARCH_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardHeader>
            <CardContent className="space-y-6">
              {/* Conditional rendering based on search type */}
              {formData.searchType === 'web-search' && (
                <WebResearch 
                  selectedResearch={selectedResearch}
                  onResearchSaved={onResearchSaved}
                  isGenerating={isGenerating}
                />
              )}
              
              {formData.searchType === 'social-media-search' && (
                <SocialResearch 
                  selectedResearch={selectedResearch}
                  onResearchSaved={onResearchSaved}
                  isGenerating={isGenerating}
                />
              )}
              
              {formData.searchType === 'search-by-url' && (
                <SiteSearch 
                  selectedResearch={selectedResearch}
                  selectedSiteResearch={selectedSiteResearch}
                  onResearchSaved={onResearchSaved}
                  onSiteResearchSaved={(siteResearch) => {
                    setCurrentSiteResearchId(siteResearch.id);
                    onSiteResearchSaved?.(siteResearch);
                  }}
                  isGenerating={isGenerating}
                />
              )}
          </CardContent>
        </Card>
      )}

      {/* Research Results */}
      <ResearchResults 
        results={results}
        formData={formData}
        accountId={accountId}
      />

      {/* Content Suggestions */}
      <ContentSuggestions 
        suggestions={contentSuggestions}
        formData={formData}
      />

      {/* Site Research Results */}
      <SiteResearchResults 
        results={siteResults}
        isVisible={!!selectedSiteResearch}
      />
    </div>
  );
} 