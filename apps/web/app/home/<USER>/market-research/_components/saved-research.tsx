'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Trash2, ExternalLink } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { cn } from '@kit/ui/utils';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { getSavedResearchAction, removeSavedResearchAction } from '../_lib/server-actions';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';

interface SavedResearchItem {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string;
  title: string;
  description: string;
  source: string;
  source_url?: string;
  relevance_score: number;
  research_type: 'pain-points' | 'trending-topics' | 'recent-news';
  time_filter: string;
  created_at: string;
  updated_at: string;
}

export function SavedResearch() {
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';

  const zero = useZero();

  const [savedResearch] = useZeroQuery(zero.query.saved_research.where('archived', '=', false), {
    ttl: '1d'
  });

  const handleRemove = async (item: SavedResearchItem) => {
    try {
      
      await removeSavedResearchAction({
        accountId,
        icpId: item.icp_id,
        personaId: item.persona_id,
        title: item.title,
        researchType: item.research_type,
      });

      zero.mutate.saved_research.update({
        id: item.id,
        values: {
          archived: true
        }
      });

      toast.success('Research item archived successfully');
    } catch (error) {
      console.error('Error removing saved research:', error);
      toast.error('Failed to remove research item');
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'pain-points':
        return 'Pain Points';
      case 'trending-topics':
        return 'Trending Topics';
      case 'recent-news':
        return 'Recent News';
      default:
        return type;
    }
  };

  const getTypeVariant = (type: string) => {
    switch (type) {
      case 'pain-points':
        return 'destructive';
      case 'trending-topics':
        return 'default';
      case 'recent-news':
        return 'secondary';
      default:
        return 'outline';
    }
  };



  return (
    <div className="space-y-6">
      <If condition={savedResearch.length === 0}>
        <div className="text-center py-12 space-y-4">
          <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              <Trans i18nKey="marketResearch:noSavedItems" defaults="No saved research items" />
            </h3>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              <Trans 
                i18nKey="marketResearch:noSavedItemsDescription" 
                defaults="Generate research insights and save them to view them here later." 
              />
            </p>
          </div>
        </div>
      </If>

      <If condition={savedResearch.length > 0}>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              <Trans i18nKey="marketResearch:savedResearch" defaults="Saved Research" />
            </h3>
            <Badge variant="outline">{savedResearch.length} items</Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {savedResearch.map((item) => (
              <Card key={item.id} className="h-full relative group">
                <CardContent className="p-4 space-y-3">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between gap-2">
                      <h4 className="font-bold text-sm leading-tight flex-1">
                        {item.title}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        // @ts-expect-error - item.persona_id is not typed
                        onClick={() => handleRemove(item)}
                        // disabled={removingId === item.id}
                        className={cn(
                          "h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",
                          "hover:bg-destructive hover:text-destructive-foreground",
                          // removingId === item.id && "opacity-100"
                        )}
                        title="Remove from saved"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <Badge 
                        variant={getTypeVariant(item.research_type || '') as any}
                        className="text-xs"
                      >
                        {getTypeLabel(item.research_type || '')}
                      </Badge>
                      <Badge 
                        variant={item.relevance_score && item.relevance_score >= 8 ? 'default' : item.relevance_score && item.relevance_score >= 6 ? 'secondary' : 'outline'}
                        className="text-xs"
                      >
                        Score: {item.relevance_score}/10
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-sm text-foreground leading-relaxed">
                    {item.description}
                  </p>
                  
                  <div className="space-y-2 mt-auto pt-2">
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{item.time_filter}</span>
                      <span>{new Date(item.created_at || '').toLocaleDateString()}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-muted-foreground flex-1">
                        {item.source}
                      </p>
                      <If condition={!!item.source_url}>
                        <Button
                          variant="ghost"
                          size="sm"
                          asChild
                          className="h-6 w-6 p-0"
                        >
                          <a 
                            href={item.source_url || ''} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            title="Open source"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        </Button>
                      </If>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </If>
    </div>
  );
} 