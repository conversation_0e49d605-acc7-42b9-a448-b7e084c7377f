'use client';

import { useState, useTransition } from 'react';
import { But<PERSON> } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Badge } from '@kit/ui/badge';
import { toast } from '@kit/ui/sonner';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Loader2 } from 'lucide-react';
import { useZero } from '~/hooks/use-zero';
import { useUser } from '@kit/supabase/hooks/use-user';

const TIME_FILTERS = [
  'Last 3 months',
  'Last 6 months', 
  'Last 12 months'
];

const RESEARCH_TYPES = [
  { value: 'pain-points', label: 'Pain Points' },
  { value: 'trending-topics', label: 'Trending Topics' },
  { value: 'recent-news', label: 'Recent News' }
];

interface WebResearchFormData {
  icpId: string;
  personaId: string;
  timeFilter: string;
  type: string;
  topic: string;
}

interface ICP {
  id: string;
  name: string;
}

interface Persona {
  id: string;
  name: string;
  role: string;
}

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: 'pain-points' | 'trending-topics' | 'recent-news';
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface WebResearchProps {
  selectedResearch?: GeneratedResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  isGenerating?: boolean;
}

function useICPs(accountId: string) {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ['icps', accountId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('icps')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch ICPs: ${error.message}`);
      }

      return data as ICP[];
    },
    enabled: !!accountId,
  });
}

function usePersonas(accountId: string, icpId?: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['personas', accountId, icpId],
    queryFn: async () => {
      let query = supabase
        .from('personas')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (icpId) {
        query = query.eq('icp_id', icpId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch personas: ${error.message}`);
      }

      return data as Persona[];
    },
    enabled: !!accountId,
  });
}

export function WebResearch({ selectedResearch, onResearchSaved, isGenerating }: WebResearchProps) {
  const [pending, startTransition] = useTransition();
  const [formData, setFormData] = useState<WebResearchFormData>({
    icpId: selectedResearch?.icp_id || '',
    personaId: selectedResearch?.persona_id || '',
    timeFilter: selectedResearch?.time_filter || '',
    type: selectedResearch?.research_type || '',
    topic: selectedResearch?.topic || ''
  });

  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const { data: user } = useUser();
  const zero = useZero();

  const { data: icps = [], isLoading: icpsLoading } = useICPs(accountId);
  const { data: personas = [], isLoading: personasLoading } = usePersonas(accountId, formData.icpId);

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: ''
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handleTimeFilterChange = (value: string) => {
    setFormData(prev => ({ ...prev, timeFilter: value }));
  };

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }));
  };

  const handleTopicChange = (value: string) => {
    setFormData(prev => ({ ...prev, topic: value }));
  };

  const handleSubmit = () => {
    if (!isFormValid || !zero || !user) return;

    startTransition(async () => {
      try {
        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);
        const researchTitle = `${selectedType?.label || 'Research'} - ${selectedICP?.name || 'Unknown ICP'}`;
        const researchId = crypto.randomUUID();

        console.log('Creating web research with Zero mutation...');

        zero.mutate.generated_research.insert({
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type,
          time_filter: formData.timeFilter,
          title: researchTitle,
          topic: formData.topic,
          created_by: user.id,
        });

        const placeholderResearch: GeneratedResearch = {
          id: researchId,
          account_id: accountId,
          icp_id: formData.icpId,
          persona_id: formData.personaId === 'no-persona' ? null : formData.personaId,
          research_type: formData.type as 'pain-points' | 'trending-topics' | 'recent-news',
          time_filter: formData.timeFilter,
          title: researchTitle,
          results: [],
          content_suggestions: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          topic: formData.topic,
        };

        onResearchSaved?.(placeholderResearch);
        
        toast.success('Web research generation started!', {
          description: 'Your research is being generated in the background. Results will appear shortly.'
        });

      } catch (error) {
        console.error('Error generating web research:', error);
        toast.error('Failed to generate web research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.timeFilter && formData.type;

  const selectedICP = icps.find(icp => icp.id === formData.icpId);
  const selectedPersona = formData.personaId === 'no-persona' ? null : personas.find(persona => persona.id === formData.personaId);
  const selectedType = RESEARCH_TYPES.find(type => type.value === formData.type);

  return (
    <div className="space-y-6">
      {/* ICP Selection */}
      <div className="space-y-2">
        <Label htmlFor="icp-select">Ideal Customer Profile (ICP)</Label>
        <Select 
          value={formData.icpId} 
          onValueChange={handleICPChange} 
          disabled={icpsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={icpsLoading ? "Loading ICPs..." : "Select an ICP"} />
          </SelectTrigger>
          <SelectContent>
            {icps.map(icp => (
              <SelectItem key={icp.id} value={icp.id}>
                {icp.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Persona Selection */}
      <div className="space-y-2">
        <Label htmlFor="persona-select">
          Persona <span className="text-muted-foreground">(Optional)</span>
        </Label>
        <Select 
          value={formData.personaId} 
          onValueChange={handlePersonaChange} 
          disabled={personasLoading || !formData.icpId}
        >
          <SelectTrigger>
            <SelectValue 
              placeholder={
                !formData.icpId 
                  ? "Select an ICP first" 
                  : personasLoading 
                    ? "Loading personas..." 
                    : "Select a persona (optional)"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="no-persona">
              <span className="text-muted-foreground">No specific persona</span>
            </SelectItem>
            {personas.map(persona => (
              <SelectItem key={persona.id} value={persona.id}>
                <div className="flex flex-col">
                  <span>{persona.name}</span>
                  <span className="text-xs text-muted-foreground">{persona.role}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Topic Input */}
      <div className="space-y-2">
        <Label htmlFor="topic-input">Topic</Label>
        <Input
          id="topic-input"
          type="text"
          placeholder="Enter research topic or question about ICP or persona, or leave blank for general insights."
          value={formData.topic}
          onChange={(e) => handleTopicChange(e.target.value)}
          className="w-full"
        />
      </div>

      {/* Research Type */}
      <div className="space-y-3">
        <Label>Type</Label>
        <RadioGroup 
          value={formData.type} 
          onValueChange={handleTypeChange} 
          className="grid grid-cols-1 gap-3"
        >
          {RESEARCH_TYPES.map(type => (
            <div key={type.value} className="flex items-center space-x-2">
              <RadioGroupItem value={type.value} id={type.value} />
              <Label htmlFor={type.value} className="cursor-pointer">
                {type.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>

      {/* Time Filter */}
      <div className="space-y-2">
        <Label htmlFor="time-filter">Time Filter</Label>
        <Select 
          value={formData.timeFilter} 
          onValueChange={handleTimeFilterChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time period" />
          </SelectTrigger>
          <SelectContent>
            {TIME_FILTERS.map(filter => (
              <SelectItem key={filter} value={filter}>
                {filter}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={!isFormValid || pending || !zero || !user || isGenerating}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Generating Web Research...
          </div>
        ) : (
          'Generate Web Research'
        )}
      </Button>

      {/* Selected Configuration Summary */}
      {(selectedICP || selectedPersona || selectedType) && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Research Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedICP && (
              <Badge variant="outline">ICP: {selectedICP.name}</Badge>
            )}
            {selectedPersona && (
              <Badge variant="outline">Persona: {selectedPersona.name}</Badge>
            )}
            {selectedType && (
              <Badge variant="outline">Type: {selectedType.label}</Badge>
            )}
            {formData.timeFilter && (
              <Badge variant="outline">Period: {formData.timeFilter}</Badge>
            )}
            {formData.topic && (
              <Badge variant="outline">Topic: {formData.topic}</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

