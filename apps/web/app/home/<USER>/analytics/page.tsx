'use client';
// import { getSupabaseServerClient } from "@kit/supabase/server-client";
// import { createTeamAccountsApi } from "@kit/team-accounts/api";
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { AnalyticsHeader } from './_components/analytics-header';
import { AnalyticsClientWrapper } from './_components/analytics-client-wrapper';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useState, useEffect } from 'react';

function AnalyticsPage() {
  const zero = useZero();
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [workingProfile, setWorkingProfile] = useState<any>(null);
  const [availableProfiles, setAvailableProfiles] = useState<any[]>([]);

  // Load the team workspace data
  const [profiles] = useZeroQuery(
    zero.query.ayrshare_user_profile,
    {
      ttl: '10m'
    }
  );

  console.log("All profiles:", profiles);

  // Try to find working LinkedIn profiles by testing analytics API
  useEffect(() => {
    const findWorkingProfiles = async () => {
      if (!profiles || profiles.length === 0) return;
      
      console.log('Finding working LinkedIn profiles...');
      const workingProfiles: any[] = [];
      let firstWorkingProfile: any = null;
      
      for (const profile of profiles) {
        if (profile.profileKey) {
          console.log('Testing profile:', profile.title || profile.id);
          
          try {
            const response = await fetch(`/api/analytics/linkedin?profileKey=${encodeURIComponent(profile.profileKey)}`);
            
            if (response.ok) {
              console.log('Profile works for LinkedIn analytics:', profile.title || profile.id);
              workingProfiles.push(profile);
              
              if (!firstWorkingProfile) {
                firstWorkingProfile = profile;
              }
            } else {
              console.log('Profile not compatible with LinkedIn analytics:', profile.title || profile.id);
            }
          } catch (err) {
            console.log('Error testing profile:', profile.title || profile.id, err);
          }
        }
      }
      
      console.log('Working LinkedIn profiles found:', workingProfiles.length);
      setAvailableProfiles(workingProfiles);
      setWorkingProfile(firstWorkingProfile);
    };
    
    findWorkingProfiles();
  }, [profiles]);

  // Transform working profiles to match the expected SocialProfile interface
  const transformedProfiles = availableProfiles?.map(profile => {
    console.log('Transforming LinkedIn profile:', profile);
    
    const transformed = {
      id: profile.id,
      title: profile.title || '', // Handle null title
      profileKey: profile.profileKey,
      user_id: profile.user_id,
      company_id: profile.company_id || '',
      created_at: new Date(profile.created_at).toISOString(),
      profile_name: profile.profile_name || profile.title || profile.refId || `LinkedIn Profile ${profile.id}` // Better fallback
    };
    
    console.log('Transformed LinkedIn profile:', transformed);
    return transformed;
  }) || [];

  console.log('All transformed LinkedIn profiles:', transformedProfiles);
  console.log('Number of LinkedIn profiles:', transformedProfiles.length);

  // Fetch analytics data for the first working profile
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      if (!workingProfile?.profileKey) return;
      
      setLoading(true);
      setError(null);
      
      try {
        console.log('Fetching analytics for working LinkedIn profile:', workingProfile);
        const response = await fetch(`/api/analytics/linkedin?profileKey=${encodeURIComponent(workingProfile.profileKey)}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch analytics data');
        }
        
        const data = await response.json();
        console.log("LinkedIn analytics data:", data);
        setAnalyticsData(data);
      } catch (err) {
        console.error('Error fetching LinkedIn analytics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAnalyticsData();
  }, [workingProfile]);
  
  // If no profiles exist at all, show an error message
  if (!profiles || profiles.length === 0) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:noProfilesTitle" defaults="No Social Profiles Found" />
            </AlertTitle>
            <AlertDescription>
              <Trans i18nKey="analytics:noProfilesDescription" defaults="You need to create and connect a social profile before viewing analytics. Please visit the integrations page to set up your social profiles." />
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // If no LinkedIn profiles are available, show appropriate message
  if (profiles.length > 0 && availableProfiles.length === 0 && !loading) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:noLinkedInProfilesTitle" defaults="No LinkedIn Profiles Found" />
            </AlertTitle>
            <AlertDescription>
              <Trans i18nKey="analytics:noLinkedInProfilesDescription" defaults="You have social profiles connected, but none are LinkedIn profiles. LinkedIn Analytics requires a connected LinkedIn profile. Please connect a LinkedIn profile in the integrations page." />
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // Show loading state while checking profiles or fetching data
  if (!workingProfile || (loading && !analyticsData)) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2 text-muted-foreground">
              {!workingProfile ? 'Checking LinkedIn profiles...' : 'Loading analytics...'}
            </span>
          </div>
        </PageBody>
      </>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
            </AlertTitle>
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // Only render the wrapper if we have analytics data
  if (!analyticsData) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </PageBody>
      </>
    );
  }

  return (
    <AnalyticsClientWrapper
      profiles={transformedProfiles}
      initialAnalyticsData={analyticsData}
      initialProfileKey={workingProfile.profileKey}
    />
  );
}

export default AnalyticsPage;