'use client';

import React from 'react';
import { Loader2, Sparkles } from 'lucide-react';

export function BrandGeneratingLoader() {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="max-w-md text-center space-y-6">
        <div className="relative">
          <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
            <Sparkles className="w-8 h-8 text-blue-600" />
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="w-20 h-20 text-blue-600 animate-spin" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">
            Your brand is being generated
          </h2>
          <p className="text-gray-600">
            This usually takes a few minutes. Please wait while we create your brand profile.
          </p>
        </div>
        
        <div className="flex justify-center">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
}