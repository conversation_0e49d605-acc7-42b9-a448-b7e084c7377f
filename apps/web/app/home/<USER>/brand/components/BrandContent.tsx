'use client';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { If } from '@kit/ui/if';
import { AddBrand } from './add-brand';
import { Account } from '~/types/accounts';
import { BrandDisplayWithSidebar } from './BrandDisplayWithSidebar';
import { CompanyBrand } from '../types/brand';
import { BrandGeneratingLoader } from './BrandGeneratingLoader';
import { BrandErrorState } from './BrandErrorState';

export const BrandContent = () => {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  const [companyBrand, companyBrandResult] = useZeroQuery(
    zero.query.company_brand
    ,
    {
      ttl: '10m'
    }
  );
  const [ account ] = useZeroQuery(
    zero.query.accounts
    .where('id', workspace.account.id),
    {
      ttl: '10m'
    }
  );

  // Helper function to check if brand data is complete and valid
  const isBrandDataValid = (brand: any): brand is CompanyBrand => {
    if (!brand) return false;
    
    // Check if all required sections exist
    const requiredFields = ['brand_profile', 'messaging_strategy', 'visual_identity', 'product_catalog', 'prompt_library'];
    return requiredFields.every(field => Object.prototype.hasOwnProperty.call(brand, field));
  };

  // Wait for data to load
  if (companyBrandResult.type !== 'complete') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  // No brand exists - show add brand form
  if (companyBrand.length === 0) {
    return (
      <div className="h-full">
        <AddBrand account={account[0] as Account} />
      </div>
    );
  }

  const brand = companyBrand[0] as unknown as CompanyBrand;

  // Brand is being generated - show loading state
  if (brand.is_generating) {
    return (
      <div className="h-full">
        <BrandGeneratingLoader />
      </div>
    );
  }

  // Brand data is corrupted or incomplete - show error state
  if (!isBrandDataValid(brand)) {
    return (
      <div className="h-full">
        <BrandErrorState 
          brandId={brand.id} 
          brandName={brand.brand_name}
        />
      </div>
    );
  }

  // Brand data is valid - show brand display
  return (
    <div className="h-full">
      <BrandDisplayWithSidebar companyBrand={brand} />
    </div>
  );
};
