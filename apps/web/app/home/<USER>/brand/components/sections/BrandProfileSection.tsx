'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Edit2, Save, X, Plus, Trash2 } from 'lucide-react';
import { BrandProfile } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface BrandProfileSectionProps {
  brandProfile: BrandProfile;
  brandId: string;
}

export function BrandProfileSection({ brandProfile, brandId }: BrandProfileSectionProps) {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<string, any>>({});
  const [isAddingCustomField, setIsAddingCustomField] = useState(false);
  const [customFieldName, setCustomFieldName] = useState('');
  const [attributeInputValue, setAttributeInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const zero = useZero();

  useEffect(() => {
    if (editingField && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editingField]);

  const handleEditStart = (field: string, value: any) => {
    setEditingField(field);
    setEditValues({ ...editValues, [field]: value });
  };

  const handleEditSave = (field: string) => {
    console.log(`Saving ${field}:`, editValues[field]);
    console.log(`editValues `, editValues);
    console.log(`brandProfile `, brandProfile);
    console.log(`To Save `, {
      ...brandProfile,
      [field]: editValues[field]
    });
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        brand_profile: {
          ...brandProfile,
          [field]: editValues[field]
        }
      }
    });
    setEditingField(null);
  };

  const handleEditCancel = () => {
    setEditingField(null);
    setEditValues({});
  };

  const handleAddCustomField = () => {
    if (customFieldName.trim() && !isStandardField(customFieldName)) {
      setEditingField(customFieldName);
      setEditValues({ ...editValues, [customFieldName]: '' });
      setIsAddingCustomField(false);
      setCustomFieldName('');
      console.log('Added custom field:', customFieldName);
      zero.mutate.company_brand.update({
        id: brandId,
        values: {
          brand_profile: {
            ...brandProfile,
            [customFieldName]: ''
          }
        }
      });
    }
  };

  const handleCustomFieldCancel = () => {
    setIsAddingCustomField(false);
    setCustomFieldName('');
  };

  const handleDeleteField = (field: string) => {
    if (field === 'mission' || field === 'vision') {
      return; // Cannot delete mission or vision
    }
    
    console.log(`Deleting field: ${field}`);
    const updatedProfile = { ...brandProfile };
    delete updatedProfile[field];
    
    zero.mutate.company_brand.update({
      id: brandId,
      values: {
        brand_profile: updatedProfile
      }
    });
  };

  const canDeleteField = (field: string) => {
    return field !== 'mission' && field !== 'vision';
  };

  const isStandardField = (fieldName: string) => {
    const standardFields = ['mission', 'vision', 'tagline', 'brand_attributes'];
    return standardFields.includes(fieldName);
  };

  const getCustomFields = () => {
    const standardFields = ['mission', 'vision', 'tagline', 'brand_attributes'];
    return Object.keys(brandProfile).filter(key => !standardFields.includes(key));
  };

  const renderEditableField = (
    field: string,
    label: string,
    value: any,
    isTextarea: boolean = false,
    helpText?: string
  ) => {
    const isEditing = editingField === field;
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">{label}</label>
          {!isEditing && (
            <div className="flex items-center gap-1">
              <button
                onClick={() => handleEditStart(field, value)}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              {canDeleteField(field) && (
                <button
                  onClick={() => handleDeleteField(field)}
                  className="text-gray-400 hover:text-red-600 p-1"
                  title={`Delete ${label}`}
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          )}
        </div>
        
        {helpText && (
          <p className="text-xs text-gray-500 mb-2">{helpText}</p>
        )}
        
        {isEditing ? (
          <div className="space-y-2">
            {isTextarea ? (
              <textarea
                ref={inputRef as React.RefObject<HTMLTextAreaElement>}
                value={editValues[field] || ''}
                onChange={(e) => setEditValues({ ...editValues, [field]: e.target.value })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
              />
            ) : (
              <input
                ref={inputRef as React.RefObject<HTMLInputElement>}
                type="text"
                value={editValues[field] || ''}
                onChange={(e) => setEditValues({ ...editValues, [field]: e.target.value })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            )}
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave(field)}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] flex items-center cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => handleEditStart(field, value)}
          >
            <span className="text-gray-900">
              {value ? (
                typeof value === 'object' ? (
                  Array.isArray(value) ? (
                    <div className="space-y-2">
                      {value.map((item, index) => (
                        <div key={index} className="flex items-start gap-3">
                          {typeof item === 'object' && item.name ? (
                            <>
                              <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                                {item.name}:
                              </span>
                              <span className="text-gray-700">{item.description || item.value || 'No description'}</span>
                            </>
                          ) : (
                            <span className="text-gray-700">{typeof item === 'object' ? JSON.stringify(item) : item}</span>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {Object.entries(value).map(([key, val]) => (
                        <div key={key} className="flex items-start gap-3">
                          <span className="font-medium text-gray-900 min-w-0 flex-shrink-0">
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                          </span>
                          <span className="text-gray-700">
                            {typeof val === 'object' ? JSON.stringify(val) : String(val)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )
                ) : (
                  value
                )
              ) : (
                <span className="text-gray-400 italic">Not set</span>
              )}
            </span>
          </div>
        )}
      </div>
    );
  };

  const renderBrandAttributes = () => {
    const isEditing = editingField === 'brand_attributes';
    
    const handleAttributeAdd = (value: string) => {
      if (value.trim()) {
        const currentAttributes = editValues.brand_attributes || [];
        if (!currentAttributes.includes(value.trim())) {
          setEditValues({ 
            ...editValues, 
            brand_attributes: [...currentAttributes, value.trim()] 
          });
        }
        setAttributeInputValue('');
      }
    };

    const handleAttributeRemove = (indexToRemove: number) => {
      const currentAttributes = editValues.brand_attributes || [];
      setEditValues({
        ...editValues,
        brand_attributes: currentAttributes.filter((_, index) => index !== indexToRemove)
      });
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleAttributeAdd(attributeInputValue);
      }
    };

    const handleEditStart = () => {
      setEditingField('brand_attributes');
      setEditValues({ 
        ...editValues, 
        brand_attributes: brandProfile.brand_attributes ? [...brandProfile.brand_attributes] : [] 
      });
      setAttributeInputValue('');
    };
    
    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-1">
          <label className="text-sm font-medium text-gray-700">Brand Attributes</label>
          {!isEditing && (
            <div className="flex items-center gap-1">
              <button
                onClick={handleEditStart}
                className="text-gray-400 hover:text-gray-600 p-1"
              >
                <Edit2 size={14} />
              </button>
              <button
                onClick={() => handleDeleteField('brand_attributes')}
                className="text-gray-400 hover:text-red-600 p-1"
                title="Delete Brand Attributes"
              >
                <Trash2 size={14} />
              </button>
            </div>
          )}
        </div>
        
        <p className="text-xs text-gray-500 mb-2">Key characteristics and personality traits that define your brand</p>
        
        {isEditing ? (
          <div className="space-y-3">
            {/* Input for adding new attributes */}
            <div className="flex gap-2">
              <input
                ref={editingField === 'brand_attributes' ? inputRef as React.RefObject<HTMLInputElement> : undefined}
                type="text"
                value={attributeInputValue}
                onChange={(e) => setAttributeInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type attribute and press Enter to add"
                className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => handleAttributeAdd(attributeInputValue)}
                disabled={!attributeInputValue.trim()}
                className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Add
              </button>
            </div>
            
            {/* Display current attributes as editable pills */}
            {editValues.brand_attributes?.length > 0 && (
              <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
                {editValues.brand_attributes.map((attr, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {attr}
                    <button
                      onClick={() => handleAttributeRemove(index)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <X size={12} />
                    </button>
                  </span>
                ))}
              </div>
            )}
            
            <div className="flex gap-2">
              <button
                onClick={() => handleEditSave('brand_attributes')}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Save size={14} />
                Save
              </button>
              <button
                onClick={handleEditCancel}
                className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
              >
                <X size={14} />
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="p-3 bg-gray-50 rounded-lg min-h-[44px] cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={handleEditStart}
          >
            {brandProfile.brand_attributes?.length ? (
              <div className="flex flex-wrap gap-2">
                {brandProfile.brand_attributes.map((attr, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {attr}
                  </span>
                ))}
              </div>
            ) : (
              <span className="text-gray-400 italic">No attributes set</span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderCustomFields = () => {
    const customFields = getCustomFields();
    
    return (
      <>
        {customFields.map(field => (
          <div key={field}>
            {renderEditableField(field, field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), brandProfile[field])}
          </div>
        ))}
      </>
    );
  };

  const renderAddCustomFieldForm = () => {
    return (
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Custom Field Name</label>
            <input
              type="text"
              value={customFieldName}
              onChange={(e) => setCustomFieldName(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddCustomField();
                }
              }}
              placeholder="Enter field name (e.g., company_history, core_values)"
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={handleAddCustomField}
              disabled={!customFieldName.trim() || isStandardField(customFieldName)}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <Plus size={14} />
              Add Field
            </button>
            <button
              onClick={handleCustomFieldCancel}
              className="flex items-center gap-1 px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 text-sm"
            >
              <X size={14} />
              Cancel
            </button>
          </div>
          
          {customFieldName.trim() && isStandardField(customFieldName) && (
            <p className="text-sm text-red-600">This field name is already used. Please choose a different name.</p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Brand Profile</h1>
        <p className="text-gray-600">Define your brand&apos;s core identity and values</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {renderEditableField('mission', 'Mission Statement', brandProfile.mission, true, 'Why your organization exists and its fundamental purpose')}
        {renderEditableField('vision', 'Vision Statement', brandProfile.vision, true, 'What your organization aspires to achieve in the future')}
        {renderEditableField('tagline', 'Tagline', brandProfile.tagline, false, 'A memorable phrase that captures your brand essence')}
        {renderBrandAttributes()}
        
        {/* Custom Fields */}
        {renderCustomFields()}
        
        {/* Add Custom Field Form */}
        {isAddingCustomField && renderAddCustomFieldForm()}
        
        {/* Add Custom Field Button */}
        {!isAddingCustomField && !editingField && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={() => setIsAddingCustomField(true)}
              className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Plus size={16} />
              Add Custom Field
            </button>
          </div>
        )}
      </div>
    </div>
  );
}