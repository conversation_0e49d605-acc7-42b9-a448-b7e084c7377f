'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Palette, Edit2 } from 'lucide-react';
import { VisualIdentity, ColorCategory, Color } from '../../types/brand';
import { useZero } from '~/hooks/use-zero';
import { ColorPickerDialog } from './ColorPickerDialog';

interface ColorPaletteSectionProps {
  visualIdentity: VisualIdentity;
  brandId: string;
}

export function ColorPaletteSection({ visualIdentity, brandId }: ColorPaletteSectionProps) {
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [editingColor, setEditingColor] = useState<Color | null>(null);
  const zero = useZero();

  // Helper function to get color palette data, handling both camelCase and snake_case
  const getColorPalette = () => {
    // First try the camelCase version (from database)
    if (visualIdentity.colorPalette) {
      return {
        categories: visualIdentity.colorPalette.categories || [],
        usage_guidelines: visualIdentity.colorPalette.usageGuidelines || ''
      };
    }
    
    // Fallback to snake_case version
    if (visualIdentity.color_palette) {
      return {
        categories: visualIdentity.color_palette.categories || [],
        usage_guidelines: visualIdentity.color_palette.usage_guidelines || ''
      };
    }
    
    return { categories: [], usage_guidelines: '' };
  };

  // Helper function to get category display name
  const getCategoryName = (category: ColorCategory) => {
    return category.categoryName || category.category_name || 'Unnamed Category';
  };

  // Helper function to get category ID
  const getCategoryId = (category: ColorCategory) => {
    return category.categoryId || category.category_id || 'unknown';
  };

  const handleCopyColor = async (colorValue: string) => {
    try {
      await navigator.clipboard.writeText(colorValue);
      setCopiedColor(colorValue);
      setTimeout(() => setCopiedColor(null), 2000);
    } catch (err) {
      console.error('Failed to copy color:', err);
    }
  };

  const handleEditColor = (color: Color) => {
    setEditingColor(color);
  };

  const handleSaveColor = async (updatedColor: Color) => {
    const colorPalette = getColorPalette();
    const updatedCategories = colorPalette.categories.map(category => ({
      ...category,
      colors: category.colors.map(color => 
        color.id === updatedColor.id ? updatedColor : color
      )
    }));

    const updatedColorPalette = {
      categories: updatedCategories,
      usageGuidelines: colorPalette.usage_guidelines
    };

    try {
      await zero.mutate.company_brand.update({
        id: brandId,
        values: {
          visual_identity: {
            ...visualIdentity,
            colorPalette: updatedColorPalette
          }
        }
      });
      
      setEditingColor(null);
    } catch (error) {
      console.error('Error saving color:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingColor(null);
  };

  const createDefaultColor = (): Color => {
    return {
      id: `color-${Date.now()}`,
      name: 'New Color',
      values: {
        hex: '#3B82F6',
        rgb: 'R59 G130 B246',
        cmyk: 'C76 M47 Y0 K4'
      },
      usage_guidelines: ''
    };
  };

  const handleAddFirstColor = () => {
    const newColor = createDefaultColor();
    setEditingColor(newColor);
  };

  const handleSaveNewColor = async (newColor: Color) => {
    // Create default color palette structure if it doesn't exist
    const defaultCategory: ColorCategory = {
      categoryId: 'primary',
      categoryName: 'Primary Colors',
      description: 'Main colors that represent your brand',
      colors: [newColor]
    };

    const newColorPalette = {
      categories: [defaultCategory],
      usageGuidelines: 'These are your brand colors. Use them consistently across all brand materials.'
    };

    try {
      await zero.mutate.company_brand.update({
        id: brandId,
        values: {
          visual_identity: {
            ...visualIdentity,
            colorPalette: newColorPalette
          }
        }
      });
      
      setEditingColor(null);
    } catch (error) {
      console.error('Error saving new color:', error);
    }
  };

  const handleSaveColorWrapper = async (updatedColor: Color) => {
    // Check if this is a new color being added to an empty palette
    const colorPalette = getColorPalette();
    const isNewColor = !colorPalette.categories.some(category => 
      category.colors.some(color => color.id === updatedColor.id)
    );

    if (isNewColor) {
      await handleSaveNewColor(updatedColor);
    } else {
      await handleSaveColor(updatedColor);
    }
  };



  const renderColorSwatch = (color: Color) => {
    return (
      <div key={color.id} className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow relative group">
        {/* Edit Button */}
        <button
          onClick={() => handleEditColor(color)}
          className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Edit2 size={14} />
        </button>
        
        <div className="space-y-3">
          <div 
            className="w-full h-20 rounded-lg border border-gray-200 cursor-pointer hover:ring-2 hover:ring-blue-500 hover:ring-opacity-50 transition-all"
            style={{ backgroundColor: color.values.hex }}
            onClick={() => handleCopyColor(color.values.hex)}
          />
          
          <div className="space-y-1">
            <div className="font-medium text-gray-900">{color.name}</div>
            
            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">HEX</span>
                <button
                  onClick={() => handleCopyColor(color.values.hex)}
                  className="text-gray-800 hover:text-blue-600 font-mono flex items-center gap-1 group"
                >
                  {color.values.hex}
                  {copiedColor === color.values.hex ? (
                    <span className="text-green-600 text-xs">✓</span>
                  ) : (
                    <Copy size={14} className="group-hover:text-blue-600" />
                  )}
                </button>
              </div>
              
              {color.values.rgb && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">RGB</span>
                  <button
                    onClick={() => handleCopyColor(color.values.rgb!)}
                    className="text-gray-800 hover:text-blue-600 font-mono flex items-center gap-1 group"
                  >
                    {color.values.rgb}
                    {copiedColor === color.values.rgb ? (
                      <span className="text-green-600 text-xs">✓</span>
                    ) : (
                      <Copy size={14} className="group-hover:text-blue-600" />
                    )}
                  </button>
                </div>
              )}
              
              {color.values.cmyk && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">CMYK</span>
                  <button
                    onClick={() => handleCopyColor(color.values.cmyk!)}
                    className="text-gray-800 hover:text-blue-600 font-mono flex items-center gap-1 group"
                  >
                    {color.values.cmyk}
                    {copiedColor === color.values.cmyk ? (
                      <span className="text-green-600 text-xs">✓</span>
                    ) : (
                      <Copy size={14} className="group-hover:text-blue-600" />
                    )}
                  </button>
                </div>
              )}
            </div>
            
            {color.usage_guidelines && (
              <div className="mt-2 p-2 bg-gray-50 rounded text-xs text-gray-600">
                <span className="font-medium">Usage:</span> {color.usage_guidelines}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderColorCategory = (category: ColorCategory, index: number) => {
    const categoryName = getCategoryName(category);
    const categoryId = getCategoryId(category);
    
    return (
      <div key={`${categoryId}-${index}`} className="space-y-4">
        <div className="border-b border-gray-200 pb-2">
          <h4 className="text-lg font-semibold text-gray-900">{categoryName}</h4>
          {category.description && (
            <p className="text-sm text-gray-600 mt-1">{category.description}</p>
          )}
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {category.colors?.map((color) => renderColorSwatch(color))}
        </div>
      </div>
    );
  };

  const colorPalette = getColorPalette();

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center gap-2 mb-4">
          <Palette className="w-5 h-5 text-gray-700" />
          <h3 className="text-lg font-semibold text-gray-900">Color Palette</h3>
        </div>
        {colorPalette.usage_guidelines && (
              <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  Usage Guidelines
                </h4>
                <p className="text-sm text-blue-800">{colorPalette.usage_guidelines}</p>
              </div>
            )}
        {colorPalette.categories && colorPalette.categories.length > 0 ? (
          <div className="space-y-8">
            {colorPalette.categories.map((category, index) => 
              renderColorCategory(category, index)
            )}
          </div>
        ) : (
          <div 
            className="p-8 bg-gray-50 rounded-lg text-center border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-100 hover:border-gray-400 transition-colors"
            onClick={handleAddFirstColor}
          >
            <Palette className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No Color Palette Defined</h4>
            <p className="text-sm text-gray-500 mb-3">
              Click here to add your first brand color.
            </p>
            <div className="text-xs text-gray-400">
              💡 Tip: Start with your primary brand color
            </div>
          </div>
        )}
      </div>
      
      {/* Color Picker Dialog */}
      <ColorPickerDialog
        isOpen={!!editingColor}
        color={editingColor}
        onClose={handleCancelEdit}
        onSave={handleSaveColorWrapper}
      />
    </>
  );
}