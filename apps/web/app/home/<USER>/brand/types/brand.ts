
// === Visual Identity (Strongly Typed) ===

export interface ColorValue {
    hex: string;
    rgb?: string; // e.g., "R237 G71 B70"
    cmyk?: string; // e.g., "C0 M65 Y65 K7"
  }
  
  export interface Color {
    id: string; // e.g., 'antler-red'
    name: string; // e.g., "Antler Red"
    values: ColorValue;
    usage_guidelines?: string;
  }
  
  export interface ColorCategory {
    category_id?: string; // e.g., 'primary'
    categoryId?: string; // e.g., 'primary'
    category_name?: string; // e.g., "Primary Colors"
    categoryName?: string; // e.g., "Primary Colors"
    description?: string;
    colors: Color[];
  }
  
  export interface Font {
    id: string; // e.g., 'satoshi-variable'
    name: string; // e.g., "Satoshi Variable"
    family: string;
    // Path to the font file in Supabase storage, can be null until uploaded
    storage_path: string | null;
    // Roles this font plays, e.g., ["Primary Heading (H1)", "Body Text"]
    roles: string[];
    usage_notes?: string; // e.g., "For marketer use only."
  }
  
  export interface Logo {
    id: string; // e.g., 'primary-full-length'
    type: string; // e.g., "Primary Full-Length", "Brand Mark Icon"
    description: string;
    // Path to the logo file in Supabase storage, can be null until uploaded
    storage_path: string | null;
    usage_guidelines: string[];
  }
  
  export interface PhotographyGuidelines {
    style: string; // e.g., "Human-led, founder-focused"
    artDirection: string[];
    rules: {
      do: string[];
      dont: string[];
    };
  }
  
  export interface VisualIdentity {
    color_palette?: {
      categories: ColorCategory[];
      usage_guidelines: string; // Overall usage rules
    };
    colorPalette?: {
      categories: ColorCategory[];
      usageGuidelines: string; // Overall usage rules
    };
    fonts?: Font[];
    logos?: Logo[];
    photography?: PhotographyGuidelines;
    [key: string]: any; // Allows for custom user-defined fields
  }
  
  // === Product Catalog (Strongly Typed) ===
  
  export interface Product {
    id: string;
    name: string;
    description: string;
    target_audience: string;
    key_features: string[];
  }
  export type ProductCatalog = Product[];
  
  // === Prompt Library (Strongly Typed) ===
  
  export interface Prompt {
    id: string;
    name: string; // e.g., "Inspirational Founder Portrait"
    category: string; // e.g., "Photography", "Social Media Copy"
    prompt: string;
  }
  export type PromptLibrary = Prompt[];
  
  // === Flexible Brand Sections ===
  
  // Using an index signature allows for any custom keys
  export interface BrandProfile {
    mission?: string;
    vision?: string;
    tagline?: string;
    summary_paragraph?: string;
    brand_attributes?: string[];
    [key: string]: any; // Allows for custom user-defined fields
  }
  
  export interface MessagingStrategy {
    voice?: { name: string; description: string }[];
    tone?: string[];
    differentiators?: { audience: string; points: string[] }[];
    style_guide_reference?: string;
    [key: string]: any; // Allows for custom user-defined fields
  }
  
  
  // === Main Brand Object ===
  
  export interface CompanyBrand {
    id: string;
    company_id: string;
    brand_name: string;
    is_draft: boolean;
    is_generating?: boolean;
    created_at: string;
    updated_at: string;
    brand_profile: BrandProfile;
    messaging_strategy: MessagingStrategy;
    visual_identity: VisualIdentity;
    product_catalog: ProductCatalog;
    prompt_library: PromptLibrary;
  }