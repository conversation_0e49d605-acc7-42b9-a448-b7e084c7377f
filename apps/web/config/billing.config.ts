/**
 * This is a sample billing configuration file. You should copy this file to `billing.config.ts` and then replace
 * the configuration with your own billing provider and products.
 */
import { BillingProviderSchema, createBillingSchema } from '@kit/billing';

// The billing provider to use. This should be set in the environment variables
// and should match the provider in the database. We also add it here so we can validate
// your configuration against the selected provider at build time.
const provider = BillingProviderSchema.parse(
  process.env.NEXT_PUBLIC_BILLING_PROVIDER,
);

export default createBillingSchema({
  // also update config.billing_provider in the DB to match the selected
  provider,
  // products configuration
  products: [
    // {
    //   id: 'freelancer',
    //   name: 'Freelancer',
    //   description:
    //     'Get Started with a full feature set for individual freelancers',
    //   currency: 'USD',
    //   badge: `Value`,
    //   plans: [
    //     {
    //       name: 'Freelancer Monthly',
    //       id: 'freelancer-monthly',
    //       paymentType: 'recurring',
    //       interval: 'month',
    //       lineItems: [
    //         {
    //           id: 'price_1NNwYHI1i3VnbZTqI2UzaHIe',
    //           name: 'Starter',
    //           cost: 49.0,
    //           type: 'flat' as const,
    //         },
    //       ],
    //     },
    //     {
    //       name: 'Freelancer Yearly',
    //       id: 'freelancer-yearly',
    //       paymentType: 'recurring',
    //       interval: 'year',
    //       lineItems: [
    //         {
    //           id: 'freelancer-yearly',
    //           name: 'Base',
    //           cost: 41.58 * 12,
    //           type: 'flat' as const,
    //         },
    //       ],
    //     },
    //   ],
    //   features: [
    //     '3 campaigns',
    //     '8x5 Chat support',
    //     'All Content Types & Channels',
    //   ],
    // },
    {
      id: 'individual',
      name: 'Individual',
      badge: `Popular`,
      highlighted: true,
      description: 'Designed for founders and individual marketers managing their own brand. Create professional marketing content across multiple formats including text, images, and videos. Personalized to your brand voice with more content generation capabilities than the free tier.',
      currency: 'USD',
      plans: [
        {
          name: 'Individual Monthly',
          id: 'individual-monthly',
          paymentType: 'recurring',
          interval: 'month',
          trialDays: 7,
          lineItems: [
            {
              id: process.env.NEXT_PUBLIC_INDIVIDUAL_PRICE_ID || '',
              name: 'Base',
              cost: 59.0,
              type: 'flat',
            },
          ],
        },
        // {
        //   name: 'Creator Yearly',
        //   id: 'creator-yearly',
        //   paymentType: 'recurring',
        //   interval: 'year',
        //   lineItems: [
        //     {
        //       id: 'price_1RGd1KBlJfcY7C7NWU0gRCia',
        //       name: 'Base',
        //       cost: 124.92 * 12,
        //       type: 'flat',
        //     },
        //   ],
        // },
      ],
      features: [
        'Fair Usage Limits',
        '24x6 Chat support',
        'Individuals',
      ],
    },
    {
      id: 'small_team',
      name: 'Small Team',
      description: 'Built for seed-stage startups with small marketing teams. Includes collaboration features for seamless content creation and approval workflows. Supports multiple brands and products with expanded social media and video content options. Ideal for growing teams that need to scale content production efficiently.',
      currency: 'USD',
      badge: `Popular`,
      plans: [
        {
          name: 'Small Team',
          id: 'small-team-monthly',
          trialDays: 7,
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: process.env.NEXT_PUBLIC_SMALL_TEAM_PRICE_ID || '',
              name: 'Base',
              cost: 199.0,
              type: 'flat',
            },
          ],
        },
        // {
        //   name: 'Growth Yearly',
        //   id: 'growth-yearly',
        //   paymentType: 'recurring',
        //   interval: 'year',
        //   lineItems: [
        //     {
        //       id: 'price_1PGOAVI1i3VnbZTqc69xaypp',
        //       name: 'Base',
        //       cost: 415.83 * 12,
        //       type: 'flat',
        //     },
        //   ],
        // },
      ],
      features: [
        'Everything in Creator',
        'Greater content generation capabilities',
        // '24x7 Chat support',
        'Unlimited Team members and guests',
      ],
    },
  ],
});
